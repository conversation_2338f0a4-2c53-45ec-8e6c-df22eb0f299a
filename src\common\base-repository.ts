import type { PartialModelObject, Query<PERSON><PERSON>er, TransactionOrKnex } from "objection";
import type { BaseModel } from "./base-model";

export class BaseRepository<TModel extends BaseModel> {
    constructor(
        protected readonly model: { new(): TModel } & typeof BaseModel,
        protected readonly schema: string = "public",
    ) {}

    protected $getBaseQuery(trx?: TransactionOrKnex) {
        return this.model
            .query<TModel>(trx)
            .withSchema(this.schema) as unknown as QueryBuilder<TModel, TModel[]>;
    }

    protected $getWhereQuery(
        params?: PartialModelObject<TModel>,
        trx?: TransactionOrKnex,
    ) {
        const query = this.$getBaseQuery(trx);

        if (params) {
            return query.where(params);
        }

        return query;
    }

    protected $getManyQuery(
        params?: PartialModelObject<TModel>,
        trx?: TransactionOrKnex,
    ) {
        return this.$getWhereQuery(params, trx);
    }

    async $getMany(
        params?: PartialModelObject<TModel>,
        trx?: TransactionOrKnex,
    ) {
        return await this.$getManyQuery(params, trx);
    }

    protected $getOneQuery(
        params?: PartialModelObject<TModel>,
        trx?: TransactionOrKnex,
    ) {
        return this
            .$getManyQuery(params, trx)
            .limit(1)
            .first();
    }

    async $getOne(
        params?: PartialModelObject<TModel>,
        trx?: TransactionOrKnex,
    ) {
        return await this.$getOneQuery(params, trx) ?? null;
    }

    protected $createManyQuery(
        dtos: PartialModelObject<TModel>[],
        trx?: TransactionOrKnex,
    ) {
        return this
            .$getBaseQuery(trx)
            .insert(dtos);
    }

    async $createMany(
        dtos: PartialModelObject<TModel>[],
        trx?: TransactionOrKnex,
    ) {
        return await this.$createManyQuery(dtos, trx);
    }

    protected $createOneQuery(
        dto: PartialModelObject<TModel>,
        trx?: TransactionOrKnex,
    ) {
        return this
            .$getBaseQuery(trx)
            .insert(dto);
    }

    async $createOne(
        dto: PartialModelObject<TModel>,
        trx?: TransactionOrKnex,
    ) {
        return await this.$createOneQuery(dto, trx);
    }

    protected $updateQuery(
        dto: PartialModelObject<TModel>,
        params?: PartialModelObject<TModel>,
        trx?: TransactionOrKnex,
    ) {
        return this
            .$getWhereQuery(params, trx)
            .update(dto);
    }

    async $update(
        dto: PartialModelObject<TModel>,
        params?: PartialModelObject<TModel>,
        trx?: TransactionOrKnex,
    ) {
        return await this.$updateQuery(dto, params, trx);
    }

    protected $deleteQuery(
        params: PartialModelObject<TModel>,
        trx?: TransactionOrKnex,
    ) {
        return this
            .$getWhereQuery(params, trx)
            .delete();
    }

    async $delete(
        params: PartialModelObject<TModel>,
        trx?: TransactionOrKnex,
    ) {
        return await this.$deleteQuery(params, trx);
    }
}
