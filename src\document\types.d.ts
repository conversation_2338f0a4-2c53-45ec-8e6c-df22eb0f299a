import type { BaseEntity, BaseModel, BaseRepository } from "../common";

export type Document = BaseEntity & {
    fdocPackageId: string;
    fdocDocumentId: string;
    name: string;
    filePath: string | null;
    medialogFilePath: string | null;
    isDownloaded: boolean;
};

export type CreateDto = Pick<
    Document,
    | "fdocPackageId"
    | "fdocDocumentId"
    | "name"
    | "filePath"
    | "medialogFilePath"
>;

export type UpdateDto = Partial<Pick<Document, "isDownloaded">>;

export type DocumentRepository = BaseRepository<BaseModel & Document> & {
    getSignedButNotDownloaded(): Promise<Document[]>;
};
