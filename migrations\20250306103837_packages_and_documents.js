/** @param { import("knex").Knex } knex */
export async function up(knex) {
    return knex.schema

        .createTable("packages", table => {
            table.increments();
            table.timestamps();

            table.uuid("fdoc_package_id").notNullable().unique();

            table.text("url");
            table.specificType("signer_identifiers", "text[]").notNullable();

            table.text("status");
        })

        .createTable("documents", table => {
            table.increments();
            table.timestamps();

            table.uuid("fdoc_package_id").notNullable().index();
            table.uuid("fdoc_document_id").notNullable().unique();

            table.text("name").notNullable();
            table.text("file_path").notNullable();

            table.foreign("fdoc_package_id")
                .references("fdoc_package_id")
                .inTable("packages")
                .onDelete("CASCADE");

            table.unique(["fdoc_package_id", "fdoc_document_id"]);
        });
}

/** @param { import("knex").Knex } knex */
export function down(knex) {
    return knex.schema

        .dropTable("packages")
        .dropTable("documents");
}
