import type { <PERSON>doc<PERSON><PERSON> } from "./api";
import type { SmbService } from "../smb";
import type { PackageService } from "../package";
import type { DocumentService } from "../document";
import type { MedialogService } from "../medialog";
import type { OperatorTokenService } from "../operator-token";
import type { DocumentWithStatusType, DocumentWithUrlType } from "./schema";
import type { FdocAccessTokenService, PasswordCredentials } from "./access-token-service";

import fs from "fs";
import path from "path";

import { Token } from "../common";
import { ConcurrentRunner } from "../utils";
import { configProvider, type Config } from "../config";
import { TOKEN_VALIDITY_MIN_MS } from "./access-token-service";
import { SignedDocumentAdapterFactory } from "./adapters";

type OperatorId = { operatorId: number };

type SignerData = {
    id?: string;
    name?: string;
    phone?: string;

    type: "corp" | "client";
    priority: number;
};

type MedialogReference = {
    entityType: string;
    entityId: number;
};
export class FdocService {
    protected readonly signedDocumentAdapterFactory: SignedDocumentAdapterFactory;

    constructor(
        protected readonly api: FdocApi,
        protected readonly medialogService: MedialogService,
        protected readonly operatorTokenService: OperatorTokenService,
        protected readonly packageService: PackageService,
        protected readonly documentService: DocumentService,
        protected readonly smbService: SmbService,
        protected readonly accessTokenService: FdocAccessTokenService,
    ) {
        const smbClientConfig = configProvider.get("medialog:smb");

        this.medialogService.initFileClient(smbClientConfig);

        this.signedDocumentAdapterFactory = new SignedDocumentAdapterFactory(
            this.api,
            this.accessTokenService,
            this.smbService,
        );
    }

    async getCorpId(credentials: PasswordCredentials): Promise<string> {
        const result = await this.api.verifyApiKey(credentials);

        return result.corpId;
    }

    async createWebhook(params: {
        login: string;
        password: string;

        events: {
            url: string;
            code: "SetStatus" | "SetStatusPackageByClient" | "SetStatusMessage" | "CreateDocument";
        }[];
    }) {
        await this.api.createWebhook(params);
    }

    protected getDocumentName(document: { name?: string, filePath: string }) {
        return document.name ?? path.basename(document.filePath);
    }

    async getDocument(params: OperatorId & {
        medialogReference?: MedialogReference;

        entityType: DocumentWithUrlType;
        entityId: string;
    }) {
        console.debug("fdocService.getDocument.params", params);

        const accessToken: Token = await this.accessTokenService.getAccessToken(params.operatorId);

        console.debug("fdocService.token.valid", accessToken.isValid());

        accessToken.isValidForOrError(TOKEN_VALIDITY_MIN_MS);

        const result = await this.api.getDocument({
            accessToken: accessToken.value,

            entityType: params.entityType,
            entityId: params.entityId,
        });

        if (params.entityType === "package") {
            await this.packageService.update(params.entityId, {
                url: result.url,
            });
        }

        if (params.medialogReference) {
            await this.medialogService.updateUrl(
                params.medialogReference.entityType,
                params.medialogReference.entityId,
                result.url,
            );
        }
        else {
            console.warn("fdocService.getDocument.medialogUrlNotUpdated");
        }

        console.debug("fdocService.getDocument.result", result);

        return result;
    }

    async getDocumentStatus(params: {
        medialogReference?: MedialogReference;

        entityType: DocumentWithStatusType;
        entityId: string;
    }) {
        console.debug("fdocService.getDocumentStatus.params", params);

        const result = await this.api.getDocumentStatus(params);

        let packageId: string | null = null;

        if (result.documentPackage) {
            packageId = result.documentPackage.packageId;
        }
        else if (params.entityType === "package") {
            packageId = params.entityId;
        }
        else if (params.entityType === "document") {
            const document = await this.documentService.getOne(params.entityId);

            if (document) {
                packageId = document.fdocPackageId;
            }
        }

        if (packageId) {
            await this.packageService.update(packageId, {
                status: result.status,
            });
        }

        if (params.medialogReference) {
            await this.medialogService.updateStatus(
                params.medialogReference.entityType,
                params.medialogReference.entityId,
                result.status,
            );
        }
        else if (packageId) {
            await this.medialogService.updatePackageStatus(
                packageId,
                result.status,
            );
        }
        else {
            console.warn("fdocService.getDocumentStatus.medialogStatusNotUpdated");
        }

        console.debug("fdocService.getDocumentStatus.result", result);

        return result;
    }

    getFilePathSchema(filePath: string): "medialog" | "file" {
        if (filePath.startsWith("medialog://")) {
            throw new Error("\"medialog://\" schema is deprecated, use \"medialog:\" instead.");
        }

        if (filePath.startsWith("medialog:")) {
            return "medialog";
        }

        return "file";
    }

    async getFileContent(filePath: string): Promise<Buffer> {
        const schema = this.getFilePathSchema(filePath);

        if (schema === "file") {
            return await fs.promises.readFile(filePath);
        }

        const content = await this.medialogService.downloadFile(
            filePath.replace(/^medialog:/, ""),
        );

        if (!content) {
            throw new Error(`File not found: ${filePath}`);
        }

        return content;
    }

    async createDocument(params: OperatorId & {
        package: {
            medialogId?: number;

            id: string;

            name?: string;
            objectId?: string;
            needIdentification?: boolean;
            operatorAutoSign?: boolean;
        };

        documents: {
            medialogId?: number;

            id: string;
            filePath: string;

            name?: string;
            unsignExpiredDate?: Date;
        }[];

        client: {
            id: string;
            phone: string;
            name: string;

            clientRole?: string;
        };

        operator?: {
            employeePosition?: string;
        };

        payment?: {
            packagePaymentLink?: string;
            amount?: number;
        };
    }) {
        const data = {
            ...params,
            documents: await ConcurrentRunner.run(
                params.documents.map(document => async () => ({
                    ...document,
                    file: await this.getFileContent(document.filePath),
                    name: this.getDocumentName(document),
                })),
                4,
            ),
        };

        console.debug("fdocService.createDocument.params", data);

        const localPackage = await this.packageService.create({
            fdocPackageId: data.package.id,
            signerIdentifiers: [String(data.operatorId), data.client.id],
            url: null,
            status: null,
            source: "medialog",
        });

        if (data.package.medialogId) {
            await this.medialogService.linkPackage(
                data.package.medialogId,
                localPackage.fdocPackageId,
            );
        }

        for (const document of data.documents) {
            const schema = this.getFilePathSchema(document.filePath);
            const medialogFilePath = schema === "medialog"
                ? document.filePath.replace(/^medialog:/, "")
                : null;

            console.debug("fdocService.createDocument.document", {
                filePath: document.filePath,
                schema,
                medialogFilePath,
            });

            const localDocument = await this.documentService.createOne({
                fdocDocumentId: document.id,
                fdocPackageId: localPackage.fdocPackageId,

                name: document.name,

                filePath: document.filePath,
                medialogFilePath,
            });

            if (document.medialogId) {
                await this.medialogService.linkDocument(
                    document.medialogId,
                    localDocument.fdocDocumentId,
                );
            }
        }

        const accessToken: Token = await this.accessTokenService.getAccessToken(data.operatorId);

        accessToken.isValidForOrError(TOKEN_VALIDITY_MIN_MS);

        const result = await this.api.createDocument({
            accessToken: accessToken.value,

            package: data.package,
            documents: data.documents,
            client: data.client,
            operator: data.operator,
            payment: data.payment,
        });

        await this.packageService.update(localPackage.fdocPackageId, {
            url: result.url,
        });

        await this.medialogService.updatePackageUrl(
            localPackage.fdocPackageId,
            result.url,
        );

        console.debug("fdocService.createDocument.result", result);

        return result;
    }

    protected getSignerIdentifier(signer: SignerData) {
        const identifiers: string[] = [];

        if (signer.type === "client") {
            identifiers.push(signer.id!, signer.name!, signer.phone!);
        }

        if ("id" in signer) {
            identifiers.push(signer.id!);
        }
        else {
            identifiers.push(signer.name!, signer.phone!);
        }

        return `${signer.type}; ${identifiers.join(", ")}`;
    }

    async createDocumentWithSigners(params: OperatorId & {
        package: {
            medialogId?: number;

            id: string;

            name?: string;
            objectId?: string;
            needIdentification?: boolean;
            identificationType?: string;

            signers: {
                id?: string;
                name?: string;
                phone?: string;

                type: "corp" | "client";
                priority: number;

                operatorAutoSign?: boolean;
                clientRole?: string;
                employeePosition?: string;
                qesCertAlias?: string;
            }[];
        };

        documents: {
            medialogId?: number;

            id: string;
            filePath: string;

            name?: string;
            unsignExpiredDate?: Date;
        }[];

        documentTemplates: {
            id: string;

            name?: string;
            autoSendToFill?: boolean;
            unsignExpiredDate?: Date;
        }[];

        payment?: {
            packagePaymentLink?: string;
            amount?: number;
        };
    }) {
        console.debug("fdocService.createDocumentWithSigners.params", params);

        const data = {
            ...params,
            documents: await ConcurrentRunner.run(
                params.documents.map(document => async () => ({
                    ...document,
                    file: await this.getFileContent(document.filePath),
                    name: this.getDocumentName(document),
                })),
                4,
            ),
        };

        const localPackage = await this.packageService.create({
            fdocPackageId: data.package.id,
            signerIdentifiers: data.package.signers
                .map(signer => this.getSignerIdentifier(signer)),
            url: null,
            status: null,
            source: "medialog",
        });

        if (data.package.medialogId) {
            await this.medialogService.linkPackage(
                data.package.medialogId,
                localPackage.fdocPackageId,
            );
        }

        for (const document of data.documents) {
            const schema = this.getFilePathSchema(document.filePath);
            const medialogFilePath = schema === "medialog"
                ? document.filePath.replace(/^medialog:/, "")
                : null;

            console.debug("fdocService.createDocument.document", {
                filePath: document.filePath,
                schema,
                medialogFilePath,
            });

            const localDocument = await this.documentService.createOne({
                fdocDocumentId: document.id,
                fdocPackageId: localPackage.fdocPackageId,

                name: document.name,

                filePath: document.filePath,
                medialogFilePath,
            });

            if (document.medialogId) {
                await this.medialogService.linkDocument(
                    document.medialogId,
                    localDocument.fdocDocumentId,
                );
            }
        }

        const accessToken: Token = await this.accessTokenService.getAccessToken(data.operatorId);

        accessToken.isValidForOrError(TOKEN_VALIDITY_MIN_MS);

        const result = await this.api.createDocumentWithSigners({
            accessToken: accessToken.value,

            package: data.package,
            documents: data.documents,
            documentTemplates: data.documentTemplates,
            payment: data.payment,
        });

        await this.packageService.update(localPackage.fdocPackageId, {
            url: result.url,
        });

        await this.medialogService.updatePackageUrl(
            localPackage.fdocPackageId,
            result.url,
        );

        console.debug("fdocService.createDocumentWithSigners.result", result);

        return result;
    }

    async declineDocument(params: OperatorId & {
        packageGuid: string;
        declineType: string | null;
    }) {
        const accessToken: Token = await this.accessTokenService.getAccessToken(params.operatorId);

        accessToken.isValidForOrError(TOKEN_VALIDITY_MIN_MS);

        await this.api.declineDocument({
            accessToken: accessToken.value,

            packageGuid: params.packageGuid,
            declineType: params.declineType,
        });
    }

    protected getSignedDocumentAdapter(type: keyof Config["fdoc"]["signedDocuments"]) {
        const isEnabled = configProvider.get(`fdoc:signedDocuments:${type}:enabled`);

        if (!isEnabled) {
            return null;
        }

        return this.signedDocumentAdapterFactory.create(configProvider.get(`fdoc:signedDocuments:${type}:provider`));
    }

    async syncNewSignedDocuments(params: {
        operatorId?: number;
    }) {
        const besAdapter = this.getSignedDocumentAdapter("bes");
        const qesAdapter = this.getSignedDocumentAdapter("qes");

        const documents = await this.documentService.getSignedButNotDownloaded();

        console.debug("fdocService.syncNewSignedDocuments.documents", documents);

        for (const document of documents) {
            console.debug("fdocService.syncNewSignedDocuments.syncingDocument", {
                id: document.id,
                fdocDocumentId: document.fdocDocumentId,
            });

            try {
                const besDownloadMode = configProvider.get("fdoc:signedDocuments:bes:downloadMode");

                const files = await Promise.all([
                    (besDownloadMode === "on-demand") ? null : besAdapter?.downloadFiles({
                        document,
                        operatorId: params.operatorId,
                        type: "bes",
                    }),
                    qesAdapter?.downloadFiles({
                        document,
                        operatorId: params.operatorId,
                        type: "qes",
                    }),
                ]).then(e => e.map(e => e?.files ?? []).flat());

                console.debug(
                    "fdocService.syncNewSignedDocuments.downloadedFiles",
                    files,
                );

                const medialogFilePath = document.medialogFilePath
                    ? path.win32.dirname(document.medialogFilePath)
                    : "//FDOC_MANUAL_DOCS";

                console.debug("fdocService.syncNewSignedDocuments.medialogFilePath", medialogFilePath);

                await Promise.all(
                    files.map(async file => {
                        const fullMedialogFileName = path.win32.join(
                            medialogFilePath,
                            file.medialogFileName,
                        );

                        if (file.attachToEmk) {
                            return await this.medialogService.uploadFileAndAttach(
                                file.localPath,
                                fullMedialogFileName,
                                document.fdocDocumentId,
                            );
                        }

                        return await this.medialogService.uploadFile(
                            file.localPath,
                            fullMedialogFileName,
                        );
                    }),
                );

                await this.documentService.update(document.id, {
                    isDownloaded: true,
                });

                Promise.all(
                    files.map(file => fs.promises.unlink(file.localPath)),
                )
                    .catch(e => {
                        console.error(
                            `fdocService.syncNewSignedDocuments.unlinkingError for document id ${document.id}`,
                            e,
                        );
                    });
            }
            catch (e) {
                console.error(
                    `fdocService.syncNewSignedDocuments.syncingError for document id ${document.id}`,
                    e,
                );
            }
        }
    }

    async handleCreateDocument(data: {
        clients: {
            id: string;
            phone: string;
        }[];

        packageId: string;
        documentId: string;
        packageName: string;
    } & ({
        documentType: "AGREEMENT" | "rejection";
    } | {
        documentType: "ANNUL_AGREEMENT";
        annulAgreementInfo: {
            packageId: string;
            reason?: string;
        };
    })) {
        console.debug("fdocAdapter.downloadFiles", data);

        if (
            await this.packageService.isExists(data.packageId)
            || await this.documentService.isExists(data.documentId)
        ) {
            throw new Error("Package or document already exists");
        }

        const patientIds = data.clients.map(client => client.id);

        const localPackage = await this.packageService.create({
            fdocPackageId: data.packageId,
            signerIdentifiers: patientIds,
            url: null,
            status: null,
            source: "fdoc",
        });

        const medialogFilePath = `//FDOC_WEBHOOK_DOCS/${data.packageId}/${data.documentId}`;

        await this.documentService.createOne({
            fdocDocumentId: data.documentId,
            fdocPackageId: localPackage.fdocPackageId,

            name: data.packageName,

            filePath: null,
            medialogFilePath,
        });

        await Promise.all(
            patientIds.flatMap(patientId => [
                this.medialogService.linkWebhookPackage(
                    patientId,
                    data.packageId,
                    medialogFilePath,
                ),
                this.medialogService.linkWebhookDocument(
                    patientId,
                    data.documentId,
                    medialogFilePath,
                ),
            ]),
        );

        if (data.documentType === "ANNUL_AGREEMENT") {
            await this.medialogService.linkAnnulAgreement(
                data.packageId,
                data.annulAgreementInfo.packageId,
                data.annulAgreementInfo.reason ?? null,
                medialogFilePath,
            );
        }
    }

    async downloadBesById(params: {
        documentId: string;
        operatorId?: number;
    }) {
        console.debug("fdocService.downloadBesById", params);

        const document = await this.documentService.getOne(params.documentId);
        if (!document) {
            throw new Error(`Document not found: ${params.documentId}`);
        }

        const packageEntity = await this.packageService.getOne(document.fdocPackageId);
        if (!packageEntity || packageEntity.status !== "SIGNED") {
            throw new Error(`Document is not signed: ${params.documentId}`);
        }

        const besAdapter = this.getSignedDocumentAdapter("bes");
        if (!besAdapter) {
            throw new Error("BES adapter not available");
        }

        try {
            const besFiles = await besAdapter.downloadFiles({
                document,
                operatorId: params.operatorId,
                type: "bes",
            });

            const files = besFiles?.files ?? [];

            console.debug(
                "fdocService.downloadBesById.downloadedFiles",
                files,
            );

            const medialogFilePath = document.medialogFilePath
                ? path.win32.dirname(document.medialogFilePath)
                : "//FDOC_MANUAL_DOCS";

            console.debug("fdocService.downloadBesById.medialogFilePath", medialogFilePath);

            await Promise.all(
                files.map(async file => {
                    const fullMedialogFileName = path.win32.join(
                        medialogFilePath,
                        file.medialogFileName,
                    );

                    if (file.attachToEmk) {
                        return await this.medialogService.uploadFileAndAttach(
                            file.localPath,
                            fullMedialogFileName,
                            document.fdocDocumentId,
                        );
                    }

                    return await this.medialogService.uploadFile(
                        file.localPath,
                        fullMedialogFileName,
                    );
                }),
            );

            Promise.all(
                files.map(file => fs.promises.unlink(file.localPath)),
            )
                .catch(e => {
                    console.error(
                        "fdocService.downloadBesById.cleanupError",
                        e,
                    );
                });

            return { success: true, message: "BES downloaded successfully", filesCount: files.length };
        }
        catch (e) {
            console.error("fdocService.downloadBesById.error", {
                documentId: params.documentId,
                error: e,
            });

            throw e;
        }
    }
}
