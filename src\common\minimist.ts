// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck

"use strict";

import minimist from "minimist";

export type ParseOptions = minimist.Opts;

export type ParsedArgs = Record<string, string>;

export class Minimist {
    constructor(protected readonly options: ParseOptions = {}) {}

    protected hasKey(obj, keys) {
        let o = obj;

        keys.slice(0, -1).forEach(function (key) {
            o = o[key] || {};
        });

        const key = keys[keys.length - 1];
        return key in o;
    }

    protected isNumber(x) {
        return false;

        // if (typeof x === "number") { return true }
        // if ((/^0x[0-9a-f]+$/i).test(x)) { return true }

        // return (/^[-+]?(?:\d+(?:\.\d*)?|\.\d+)(e[-+]?\d+)?$/).test(x);
    }

    protected isConstructorOrProto(obj, key) {
        return (key === "constructor" && typeof obj[key] === "function") || key === "__proto__";
    }

    parse(args: string[]): ParsedArgs {
        const flags: {
            bools: Record<string, boolean>;
            strings: Record<string, string>;
            unknownFn: ((arg: string) => boolean) | null;
            allBools?: boolean;
        } = {
            bools: {},
            strings: {},
            unknownFn: null,
        };

        if (typeof this.options.unknown === "function") {
            flags.unknownFn = this.options.unknown;
        }

        if (typeof this.options.boolean === "boolean" && this.options.boolean) {
            flags.allBools = true;
        } else {
            [].concat(this.options.boolean).filter(Boolean).forEach(function (key) {
                flags.bools[key] = true;
            });
        }

        const aliases: Record<string, string[]> = {};

        function aliasIsBoolean(key: string) {
            return aliases[key]!.some(function (x) {
                return flags.bools[x];
            });
        }

        Object.keys(this.options.alias || {}).forEach((key) => {
            aliases[key] = [].concat(this.options.alias[key]);
            aliases[key].forEach((x) => {
                aliases[x] = [key].concat(aliases[key]!.filter((y) => x !== y));
            });
        });

        [].concat(this.options.string).filter(Boolean).forEach(function (key) {
            flags.strings[key] = true;
            if (aliases[key]) {
                [].concat(aliases[key]).forEach(function (k) {
                    flags.strings[k] = true;
                });
            }
        });

        const defaults = this.options.default || {};

        const argv = { _: [] };

        function argDefined(key, arg) {
            return (flags.allBools && (/^--[^=]+$/).test(arg))
                || flags.strings[key]
                || flags.bools[key]
                || aliases[key];
        }

        const setKey = (obj, keys, value) => {
            let o = obj;
            for (let i = 0; i < keys.length - 1; i++) {
                const key = keys[i];
                if (this.isConstructorOrProto(o, key)) { return }
                if (o[key] === undefined) { o[key] = {} }
                if (
                    o[key] === Object.prototype
                    || o[key] === Number.prototype
                    || o[key] === String.prototype
                ) {
                    o[key] = {};
                }
                if (o[key] === Array.prototype) { o[key] = [] }
                o = o[key];
            }

            const lastKey = keys[keys.length - 1];
            if (this.isConstructorOrProto(o, lastKey)) { return }
            if (
                o === Object.prototype
                || o === Number.prototype
                || o === String.prototype
            ) {
                o = {};
            }
            if (o === Array.prototype) { o = [] }
            if (o[lastKey] === undefined || flags.bools[lastKey] || typeof o[lastKey] === "boolean") {
                o[lastKey] = value;
            } else if (Array.isArray(o[lastKey])) {
                o[lastKey].push(value);
            } else {
                o[lastKey] = [o[lastKey], value];
            }
        };

        const setArg = (key, val, arg) => {
            if (arg && flags.unknownFn && !argDefined(key, arg)) {
                if (flags.unknownFn(arg) === false) { return }
            }

            const value = !flags.strings[key] && this.isNumber(val)
                ? Number(val)
                : val;
            setKey(argv, key.split("."), value);

            (aliases[key] || []).forEach(function (x) {
                setKey(argv, x.split("."), value);
            });
        };

        Object.keys(flags.bools).forEach(function (key) {
            setArg(key, defaults[key] === undefined ? false : defaults[key]);
        });

        let notFlags = [];

        if (args.indexOf("--") !== -1) {
            notFlags = args.slice(args.indexOf("--") + 1);
            args = args.slice(0, args.indexOf("--"));
        }

        for (let i = 0; i < args.length; i++) {
            const arg = args[i];
            let key;
            let next;

            if ((/^--.+=/).test(arg)) {
                // Using [\s\S] instead of . because js doesn't support the
                // 'dotall' regex modifier. See:
                // http://stackoverflow.com/a/1068308/13216
                const m = arg.match(/^--([^=]+)=([\s\S]*)$/);
                key = m[1];
                let value = m[2];
                if (flags.bools[key]) {
                    value = value !== "false";
                }
                setArg(key, value, arg);
            } else if ((/^--no-.+/).test(arg)) {
                key = arg.match(/^--no-(.+)/)[1];
                setArg(key, false, arg);
            } else if ((/^--.+/).test(arg)) {
                key = arg.match(/^--(.+)/)[1];
                next = args[i + 1];
                if (
                    next !== undefined
                    && !(/^(-|--)[^-]/).test(next)
                    && !flags.bools[key]
                    && !flags.allBools
                    && (aliases[key] ? !aliasIsBoolean(key) : true)
                ) {
                    setArg(key, next, arg);
                    i += 1;
                } else if ((/^(true|false)$/).test(next)) {
                    setArg(key, next === "true", arg);
                    i += 1;
                } else {
                    setArg(key, flags.strings[key] ? "" : true, arg);
                }
            } else if ((/^-[^-]+/).test(arg)) {
                const letters = arg.slice(1, -1).split("");

                let broken = false;
                for (let j = 0; j < letters.length; j++) {
                    next = arg.slice(j + 2);

                    if (next === "-") {
                        setArg(letters[j], next, arg);
                        continue;
                    }

                    if ((/[A-Za-z]/).test(letters[j]) && next[0] === "=") {
                        setArg(letters[j], next.slice(1), arg);
                        broken = true;
                        break;
                    }

                    if (
                        (/[A-Za-z]/).test(letters[j])
                        && (/-?\d+(\.\d*)?(e-?\d+)?$/).test(next)
                    ) {
                        setArg(letters[j], next, arg);
                        broken = true;
                        break;
                    }

                    if (letters[j + 1] && letters[j + 1].match(/\W/)) {
                        setArg(letters[j], arg.slice(j + 2), arg);
                        broken = true;
                        break;
                    } else {
                        setArg(letters[j], flags.strings[letters[j]] ? "" : true, arg);
                    }
                }

                key = arg.slice(-1)[0];
                if (!broken && key !== "-") {
                    if (
                        args[i + 1]
                        && !(/^(-|--)[^-]/).test(args[i + 1])
                        && !flags.bools[key]
                        && (aliases[key] ? !aliasIsBoolean(key) : true)
                    ) {
                        setArg(key, args[i + 1], arg);
                        i += 1;
                    } else if (args[i + 1] && (/^(true|false)$/).test(args[i + 1])) {
                        setArg(key, args[i + 1] === "true", arg);
                        i += 1;
                    } else {
                        setArg(key, flags.strings[key] ? "" : true, arg);
                    }
                }
            } else {
                if (!flags.unknownFn || flags.unknownFn(arg) !== false) {
                    argv._.push(flags.strings._ || !this.isNumber(arg) ? arg : Number(arg));
                }
                if (this.options.stopEarly) {
                    // eslint-disable-next-line prefer-spread
                    argv._.push.apply(argv._, args.slice(i + 1));
                    break;
                }
            }
        }

        Object.keys(defaults).forEach((k) => {
            if (!this.hasKey(argv, k.split("."))) {
                setKey(argv, k.split("."), defaults[k]);

                (aliases[k] || []).forEach(function (x) {
                    setKey(argv, x.split("."), defaults[k]);
                });
            }
        });

        if (this.options["--"]) {
            argv["--"] = notFlags.slice();
        } else {
            notFlags.forEach(function (k) {
                argv._.push(k);
            });
        }

        return argv;
    }
}
