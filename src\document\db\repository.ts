import { BaseRepository } from "../../common";
import { DocumentModel } from "./model";

export class DocumentDatabaseRepository extends BaseRepository<DocumentModel> {
    constructor() {
        super(DocumentModel);
    }

    async getSignedButNotDownloaded(): Promise<DocumentModel[]> {
        return await this.$getManyQuery()
            .leftJoin(
                "packages",
                "packages.fdoc_package_id",
                "documents.fdoc_package_id",
            )
            .where({
                "documents.is_downloaded": false,
                "packages.status": "SIGNED",
            });
    }
}
