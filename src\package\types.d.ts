import type { BaseEntity, BaseModel, BaseRepository } from "../common";

export type Package = BaseEntity & {
    fdocPackageId: string;
    signerIdentifiers: string[];

    url: string | null;
    status: string | null;
    source: string | null;
};

export type CreateDto = Pick<
    Package,
    | "fdocPackageId"
    | "signerIdentifiers"
    | "url"
    | "status"
    | "source"
>;

export type UpdateDto = Partial<
    Pick<
        Package,
        | "url"
        | "status"
        | "source"
    >
>;

export type PackageRepository = BaseRepository<BaseModel & Package>;
