import { ConfigProvider } from "@itmedsib/config";

type DbConfig<T extends string> = {
    client: T;
    connection: {
        host: string;
        port: number;
        database: string;
        user: string;
        password: string;
    };
    pool: {
        min: number;
        max: number;
        idleTimeoutMillis: number;
    };
}

export type Config = {
    server: {
        port: number;
        externalAddress: string;
    };

    database: DbConfig<string> & {
        migrations: {
            tableName: "knex_migrations";
        };
    };

    medialog: {
        database: DbConfig<"mssql">;
        queriesPath: string;
        smb: {
            address: string;
            username: string;
            password: string;
            domain: string;

            assmed: {
                signedDocumentsRoot: string;
            };
        };
    };

    fdoc: {
        auth: {
            login: string;
            password: string;
            appCode: string;
            apiKey: string;
            corpId: string;
        };
        url: {
            base: string;
            archivesBase: string;
        };
        signedDocuments: {
            bes: {
                enabled: boolean;
                provider: "fdoc" | "assmed";
            };
            qes: {
                enabled: boolean;
                provider: "fdoc" | "assmed";
            };
        };
    };
};

export const configProvider = new ConfigProvider<Config>()
    .argv()
    .load();
