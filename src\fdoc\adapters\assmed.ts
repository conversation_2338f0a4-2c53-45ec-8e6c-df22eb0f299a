import type { SmbService } from "../../smb";
import type { DownloadFilesParams, SignedDocumentAdapter, SignedDocuments } from "./adapter";

import path from "path";

export class AssMedAdapter implements SignedDocumentAdapter {
    constructor(
        protected readonly smbService: SmbService,
    ) { }

    async downloadFiles({ document, type }: DownloadFilesParams): Promise<SignedDocuments> {
        const startDate = new Date(document.createdAt);
        startDate.setDate(startDate.getDate() - 1);
        startDate.setHours(12, 0, 0, 0);

        const maxDate = new Date();
        maxDate.setDate(maxDate.getDate() + 60);

        while (startDate < maxDate) {
            const signedFiles = await this.smbService.downloadAssMedSignedFiles({
                year: startDate.getFullYear(),
                month: startDate.getMonth() + 1,
                day: startDate.getDate(),
                packageId: document.fdocPackageId,
                documentId: document.fdocDocumentId,
            });

            console.debug(
                "[assmed-adapter] downloaded files",
                {
                    startDate,
                    signedFiles,
                },
            );

            if (!signedFiles) {
                startDate.setDate(startDate.getDate() + 1);

                continue;
            }

            const {
                pdf,
                pdfSig,
                printedForm,
            } = signedFiles;

            const originalFileName = path.parse(document.name).name;

            const pdfMedialogFileName = `${originalFileName}_${document.fdocDocumentId}.pdf`;
            const pdfSigMedialogFileName = `${originalFileName}_${document.fdocDocumentId}.pdf.sig`;
            const printedFormMedialogFileName = `${originalFileName}_printed_form_${type}.pdf`;

            return {
                files: [
                    {
                        localPath: pdf,
                        medialogFileName: pdfMedialogFileName,
                        attachToEmk: false,
                    },
                    {
                        localPath: pdfSig,
                        medialogFileName: pdfSigMedialogFileName,
                        attachToEmk: false,
                    },
                    {
                        localPath: printedForm,
                        medialogFileName: printedFormMedialogFileName,
                        attachToEmk: true,
                    },
                ],
            };
        }

        console.dir(
            {
                document,
                type,
            },
            {
                depth: null,
            },
        );

        throw new Error("Not found ASS-Med signed files in expected place");
    }
}
