import path from "path";
import { readFile } from "fs/promises";
import { MedialogQuery } from "medialog";
import { MedialogFile } from "@itmedsib/medialog";
import SambaClient from "samba-client";

export type SmbClientOptions = Normalize<ConstructorParameters<typeof SambaClient>[0]>

export class MedialogService extends MedialogQuery {
    protected medialogFile!: MedialogFile;

    initFileClient(sambaOptions: SmbClientOptions) {
        this.medialogFile = new MedialogFile(
            new SambaClient(sambaOptions),
        );
    }

    /**
     * @returns downloaded file content
     */
    async downloadFile(remotePath: string, localPath?: string): Promise<Buffer | null> {
        return await this.medialogFile.download(remotePath, localPath);
    }

    async uploadFile(localPath: string, remotePath: string) {
        await this.medialogFile.upload(localPath, remotePath);
    }

    get queryBuilder() {
        return this.queryBuider;
    }

    exec<T>(query: string[], data: Record<string, unknown> | null | undefined, first: true): Promise<T | null>;
    exec<T>(query: string[], data?: Record<string, unknown> | null, first?: false): Promise<T[]>;
    async exec<T>(query: string[], data: Record<string, unknown>, first: boolean = false) {
        const rawSql = await readFile(
            path.join(
                this.queriesPath,
                query.join(path.sep) + ".sql",
            ),
            "utf-8",
        );

        const result = await this.queryBuilder.raw(rawSql, data) as T[];

        if (first) {
            return result[0] ?? null;
        }

        return result;
    }

    async getOperatorCredentials(operatorId: number) {
        return await this.exec<{
            login: string;
            password: string;
        }>(
            ["operator", "get_credentials"],
            { operator_id: operatorId },
            true,
        );
    }

    async linkPackage(medialogId: number, fdocGuid: string) {
        await this.exec(["package", "link"], {
            id: medialogId,
            fdoc_guid: fdocGuid,
        });
    }

    async linkDocument(medialogId: number, fdocGuid: string) {
        await this.exec(["document", "link"], {
            id: medialogId,
            fdoc_guid: fdocGuid,
        });
    }

    async linkWebhookPackage(
        patientId: string,
        packageId: string,
        medialogFilePath: string,
    ) {
        await this.exec(["package", "link_webhook"], {
            patient_id: patientId,
            fdoc_guid: packageId,
            medialog_file_path: medialogFilePath,
        });
    }

    async linkWebhookDocument(
        patientId: string,
        documentId: string,
        medialogFilePath: string,
    ) {
        await this.exec(["document", "link_webhook"], {
            patient_id: patientId,
            fdoc_guid: documentId,
            medialog_file_path: medialogFilePath,
        });
    }

    async linkAnnulAgreement(
        packageId: string,
        annuledPackageId: string,
        reason: string | null,
        medialogFilePath: string,
    ) {
        await this.exec(["package", "link_annul_agreement"], {
            fdoc_guid: packageId,
            annuled_fdoc_guid: annuledPackageId,
            reason,
            medialog_file_path: medialogFilePath,
        });
    }

    async updateUrl(
        entityType: string,
        entityId: number,
        url: string,
    ) {
        await this.exec(["update_url"], {
            entity_type: entityType,
            entity_id: entityId,
            url,
        });
    }

    async updateStatus(
        entityType: string,
        entityId: number,
        status: string,
    ) {
        await this.exec(["update_status"], {
            entity_type: entityType,
            entity_id: entityId,
            status,
        });
    }

    async updatePackageUrl(
        fdocGuid: string,
        url: string,
    ) {
        await this.exec(["package", "update_url"], {
            fdoc_guid: fdocGuid,
            url,
        });
    }

    async updatePackageStatus(
        fdocGuid: string,
        status: string,
    ) {
        await this.exec(["package", "update_status"], {
            fdoc_guid: fdocGuid,
            status,
        });
    }

    async attachFile(
        fdocDocumentId: string,
        filePath: string,
    ) {
        await this.exec(["document", "attach_file"], {
            fdoc_document_guid: fdocDocumentId,
            file_path: filePath,
        });
    }

    async uploadFileAndAttach(
        localPath: string,
        remotePath: string,
        fdocDocumentId: string,
    ) {
        console.debug("medialogService.uploadFileAndAttach.uploadingFile", {
            localPath,
            remotePath,
        });

        await this.uploadFile(localPath, remotePath);

        console.debug("medialogService.uploadFileAndAttach.attachingFile", {
            fdocDocumentId,
            remotePath,
        });

        await this.attachFile(fdocDocumentId, remotePath);
    }
}
