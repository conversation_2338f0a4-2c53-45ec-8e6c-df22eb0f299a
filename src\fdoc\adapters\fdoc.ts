import type { Token } from "../../common";
import type { FdocAccessTokenService } from "../access-token-service";
import type { FdocApi } from "../api";
import type { DownloadFilesParams, SignedDocumentAdapter, SignedDocuments } from "./adapter";

import { tmpdir } from "os";
import path from "path";
import fs from "fs";

import { TOKEN_VALIDITY_MIN_MS } from "../access-token-service";

export class FdocAdapter implements SignedDocumentAdapter {
    constructor(
        protected readonly fdocApi: FdocApi,
        protected readonly fdocAccessTokenService: FdocAccessTokenService,
    ) { }

    async downloadFiles(params: DownloadFilesParams): Promise<SignedDocuments> {
        if (!params.operatorId) {
            throw new Error("FDoc adapter requires operatorId");
        }

        console.debug("fdocAdapter.downloadFiles", params);

        const accessToken: Token = await this.fdocAccessTokenService.getAccessToken(params.operatorId);

        accessToken.isValidForOrError(TOKEN_VALIDITY_MIN_MS);

        console.debug("fdocAdapter.downloadFiles.gettingArchivesDocumentToken...");

        const { token } = await this.fdocApi.getArchivesDocumentToken({
            accessToken: accessToken.value,

            entityType: "document",
            entityId: params.document.fdocDocumentId,

            isNeedQesSign: params.type === "qes",
        });

        console.debug("fdocAdapter.downloadFiles.gettingArchivesDownloadData", {
            token,
        });

        const { data } = await this.fdocApi.getArchivesDownloadDataWithWaiting({
            token,
        });

        console.debug("fdocAdapter.downloadFiles.gettingArchivesDownloadData.data", {
            data,
        });

        if (data.archivesCount > 1) {
            throw new Error("FDoc adapter supports only plain files, not archived ones.");
        }

        const fileData = data.packageArchives[0];

        if (!fileData) {
            throw new Error("Somewhy packageArchives is empty.");
        }

        console.debug("fdocAdapter.downloadFiles.downloadArchivesDocument", {
            fileData,
        });

        const response = await this.fdocApi.downloadArchivesDocument({
            token,
            guid: fileData.guid,
        });

        const originalFileName = path.parse(fileData.name).name;
        const medialogFileName = `${originalFileName}_${params.document.fdocDocumentId}_${params.type}.pdf`;

        const localFilePath = path.join(tmpdir(), medialogFileName);

        const writeStream = fs.createWriteStream(localFilePath);

        response.pipe(writeStream);

        return new Promise((resolve, reject) => {
            writeStream.on("finish", () => {
                resolve({
                    files: [{
                        localPath: localFilePath,
                        medialogFileName,
                        attachToEmk: true,
                    }],
                });
            });

            writeStream.on("error", reject);
        });
    }
}
