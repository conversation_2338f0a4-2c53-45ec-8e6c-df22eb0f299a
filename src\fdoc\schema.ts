import { z } from "zod";
import { <PERSON>od<PERSON><PERSON><PERSON> } from "..";

const apiKey = z.string().nonempty();
const corpId = z.string().nonempty();

const entityId = z.string().nonempty();
const documentId = z.string().uuid();

export type DocumentWithUrlType = z.infer<typeof documentWithUrlType>;
const documentWithUrlType = z.enum([
    "package",
    "object",
    "client",
    "clientPhone",
    "employee",
    "corporation",
]);

export type DocumentWithStatusType = z.infer<typeof documentWithStatusType>;
const documentWithStatusType = z.enum([
    "package",
    "document",
]);

const priority = z.number().int();

const flatSigner = z.object({
    id: z.string().optional(),
    name: z.string().optional(),
    phone: z.string().optional(),

    type: z.enum(["corp", "client"]),
    priority,

    operatorAutoSign: z.boolean().optional(),

    clientRole: z.string().optional(),
    employeePosition: z.string().optional(),

    qesCertAlias: z.string().optional(),
});

const structuredSigner = z.union([
    z.intersection(
        z.object({
            type: z.literal("corp"),
            priority,
        }),

        z.union([
            z.object({
                id: z.string(),
                // name: z.string().nullable().optional(),
                // phone: z.string().nullable().optional(),
            }),

            z.object({
                // id: z.null().optional(),
                name: z.string(),
                phone: ZodHelper.employeePhoneSchema,
            }),
        ]),
    ),

    z.object({
        type: z.literal("client"),
        id: z.string().nonempty(),
        name: z.string().nonempty(),
        phone: ZodHelper.clientPhoneSchema,
        priority,
    }),
]);

const signer = flatSigner.pipe(structuredSigner);

export const defaultError = z.object({
    message: z.string().nonempty(),
});

export const fallbackError = z.object({
    timestamp: z.coerce.date(),
    status: z.number().int(),
    error: z.string(),
    path: z.string(),
});

export const webhookChannelTypeCode = z.enum([
    "API",
    "LK",
    "BITRIX24",
]);

export const webhookEventCode = z.enum([
    "SetStatus",
    "SetStatusPackageByClient",
    "SetStatusMessage",
    "CreateDocument",
]);

export const Schema = {
    verifyApiKey: {
        request: z.object({
            query: z.object({
                apiKey,
            }),

            headers: z.object({
                "Authorization": z.string(),
                "Content-Type": z.string().default("application/json"),
            }),
        }),

        response: {
            success: z.object({
                corpId,
            }),
        },
    },

    corp: {
        webhooks: {
            create: {
                request: z.object({
                    headers: z.object({
                        "Authorization": z.string(),
                        "Content-Type": z.string().default("application/json"),
                    }),

                    body: z.object({
                        apiKey,
                        events: z.array(
                            z.object({
                                url: z.string().url(),
                                code: webhookEventCode,
                            }),
                        ),
                        channelTypeCode: webhookChannelTypeCode.default("API"),
                    }),
                }),

                response: {
                    success: z.unknown(),
                },
            },
        },

        archives: {
            document: {
                getToken: {
                    request: z.object({
                        headers: z.object({
                            "Authorization": z.string(),
                            "Content-Type": z.string().default("application/json"),
                        }),

                        body: z.object({
                            needQesSign: z.boolean(),

                            idType: z.enum(["package", "document"]),
                            id: z.string().uuid(),
                        }),
                    }),

                    response: {
                        success: z.object({
                            token: z.string().nonempty(),
                        }),
                    },
                },
            },
        },
    },

    operator: {
        accessToken: {
            request: z.object({
                headers: z
                    .object({
                        "Content-Type": z.string().default("application/json"),
                    })
                    .default({}),

                body: z.object({
                    app: z.string().nonempty(),
                    apiKey,
                    corpId,

                    grantType: z.enum(["password", "refreshToken"]),
                    grant: z.string().nonempty(),
                }),
            }),

            response: {
                success: z.object({
                    accessToken: z.string().nonempty(),
                    refreshToken: z.object({
                        value: z.string().nonempty(),
                        exp: z.coerce.date(),
                    }),
                }),
            },
        },
    },

    document: {
        get: {
            request: z.object({
                headers: z.object({
                    "Authorization": z.string(),
                    "Content-Type": z.string().default("application/json"),
                }),

                query: z.object({
                    app: z.string().nonempty(),
                    corpId: z.string().nonempty(),

                    id: entityId,
                    idType: documentWithUrlType,
                })
                    .transform(data => {
                        if (data.idType === "package") {
                            data.id = data.id.toLowerCase();
                        }

                        return data;
                    }),
            }),

            response: {
                success: z.object({
                    url: z.string().nonempty(),
                }),
            },
        },

        getStatus: {
            request: z.object({
                headers: z
                    .object({
                        "Content-Type": z.string().default("application/json"),
                    })
                    .default({}),

                query: z.object({
                    id: documentId,
                    idType: documentWithStatusType,
                }),
            }),

            response: {
                success: z.object({
                    status: z.string().nonempty(),
                    documentPackage: z.nullable(
                        z.object({
                            packageId: z.string().uuid(),
                            packageName: z.string().nonempty(),
                            documents: z.array(
                                z.object({
                                    documentId: z.string().uuid(),
                                    documentName: z.string().nonempty(),
                                    clientSigners: z.array(
                                        z.object({
                                            clientId: z.string(),
                                            clientName: z.string(),
                                            identificationStatus: z.string(),
                                        }),
                                    ),
                                }),
                            ),
                        }),
                    ),
                }),
            },
        },

        create: {
            request: z.object({
                headers: z.object({
                    "Authorization": z.string(),
                    "Content-Type": z.string().default("application/json"),
                }),

                body: z.object({
                    package: z.object({
                        id: z.string().uuid(),

                        name: z.string().optional(),
                        objectId: z.string().optional(),
                        needIdentification: z.boolean().default(false),
                        operatorAutoSign: z.boolean().default(false),
                    }),

                    documents: z.array(
                        z.object({
                            id: z.string().uuid(),
                            name: z.string().regex(/^.+?\.\w+$/), // file name with ext
                            file: z.string().nonempty(),
                            unsignExpiredDate: z.date().nullable(),
                        }),
                    ),

                    client: z.object({
                        id: z.string().nonempty(),
                        phone: ZodHelper.clientPhoneSchema,
                        name: z.string().nonempty(),
                        clientRole: z.string().optional(),
                    }),

                    operator: z.optional(
                        z.object({
                            employeePosition: z.string().optional(),
                        }),
                    ),

                    payment: z.optional(
                        z.object({
                            packagePaymentLink: z.string().nonempty().optional(),
                            amount: z.number().nonnegative().optional(),
                        }),
                    ),
                }),
            }),

            response: {
                success: z.object({
                    url: z.string().url(),
                }),
            },
        },

        createWithSigners: {
            request: z.object({
                headers: z.object({
                    "Authorization": z.string(),
                    "Content-Type": z.string().default("application/json"),
                }),

                body: z.object({
                    addService: z.coerce.boolean(),

                    package: z.object({
                        id: z.string().uuid(),

                        name: z.string().optional(),
                        objectId: z.string().optional(),
                        needIdentification: z.boolean().default(false),
                        identificationType: z.string().optional(),

                        signers: z.array(signer),
                    }),

                    documents: z.array(
                        z.object({
                            id: z.string().uuid(),
                            name: z.string().regex(/^.+?\.\w+$/), // file name with ext
                            file: z.string().nonempty(),
                            unsignExpiredDate: z.date().nullable(),
                        }),
                    ),

                    documentTemplates: z.array(
                        z.object({
                            id: z.string().uuid(),
                            name: z.string().nonempty().optional(),
                            autoSendToFill: z.boolean().default(false),
                            unsignExpiredDate: z.date().nullable(),
                        }),
                    ),

                    payment: z.optional(
                        z.object({
                            packagePaymentLink: z.string().nonempty().optional(),
                            amount: z.number().nonnegative().optional(),
                        }),
                    ),
                }),
            }),

            response: {
                success: z.object({
                    url: z.string().url(),
                }),
            },
        },

        decline: {
            request: z.object({
                headers: z.object({
                    "Authorization": z.string(),
                    "Content-Type": z.string().default("application/json"),
                }),

                body: z.object({
                    packageGuid: documentId,
                    declineType: z.string().nonempty().nullable(),
                }),
            }),

            response: {
                success: z.unknown(),
            },
        },
    },

    archiveServer: {
        getDownloadData: {
            request: z.object({
                headers: z.object({
                    "Content-Type": z.string().default("application/json"),
                }),

                body: z.object({
                    token: z.string().nonempty(),
                }),
            }),

            response: {
                success: z.object({
                    result: z.object({
                        status: z.literal("OK"),
                    }),

                    data: z.object({
                        archivesCount: z.number().int().nonnegative(),
                        packageArchives: z.array(
                            z.object({
                                guid: z.string().uuid(),
                                name: z.string().nonempty(),
                                size: z.number().int().nonnegative(),
                            }),
                        ),
                        status: z.union([
                            z.literal("IN_PROCESS"),
                            z.literal("SUCCESS"),
                            z.literal("PARTLY"),
                            z.literal("ERROR"),
                        ]),
                    }),
                }),
            },
        },

        download: {
            request: z.object({
                params: z.object({
                    token: z.string().nonempty(),
                    guid: z.string().uuid(),
                }),
            }),
        },
    },
};
