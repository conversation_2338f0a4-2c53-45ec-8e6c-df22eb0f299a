import { z, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "..";
import { v4 as uuidv4 } from "uuid";

const url = z.string().url();
const uuid = z.string().uuid().transform(id => id.toLowerCase());

const toDate = z.string().pipe(z.coerce.date());

const entityId = z.string().nonempty();
const documentId = z.string().uuid();
const autogeneratedDocumentId = uuid
    .default(() => uuidv4())
    .transform(id => id.toLowerCase());

const corpId = z.string().nonempty();
const operatorId = z.number().nonnegative().int();

const medialogId = z.number().int().positive();

const errorMessage = z.string().nonempty();

const defaultErrorShape = {
    message: errorMessage,
};

const documentWithUrlType = z.enum([
    "package",
    "object",
    "client",
    "clientPhone",
    "employee",
    "corporation",
]);

const documentWithStatusType = z.enum([
    "package",
    "document",
]);

const medialogReference = z
    .object({
        entityType: z.string().nonempty(),
        entityId: medialogId,
    })
    .optional();

function httpResponse<
    const TStatus extends number,
    TBodyShape extends z.ZodRawShape,
>(
    status: TStatus,
    shape: {
        body: TBodyShape;
    },
) {
    return z.object({
        status: z.literal(status),
        body: z.object(shape.body),
    });
}

function ok<T extends z.ZodRawShape>(shape: T) {
    return httpResponse(200, { body: shape });
}

function badRequest(): z.ZodObject<{ status: z.ZodLiteral<400>, body: z.ZodObject<typeof defaultErrorShape> }>;
function badRequest<T extends z.ZodRawShape>(shape: T): z.ZodObject<{ status: z.ZodLiteral<400>, body: z.ZodObject<T> }>;
function badRequest(shape?: z.ZodRawShape): z.ZodObject<{ status: z.ZodLiteral<400>; body: z.ZodType }> {
    if (shape) {
        return httpResponse(400, { body: shape });
    }

    return httpResponse(400, { body: defaultErrorShape });
}

function internalServerError(): z.ZodObject<{ status: z.ZodLiteral<500>, body: z.ZodObject<{ message: z.ZodString }> }>;
function internalServerError<T extends z.ZodRawShape>(shape: T): z.ZodObject<{ status: z.ZodLiteral<500>, body: z.ZodObject<T> }>;
function internalServerError(shape?: z.ZodRawShape): z.ZodObject<{ status: z.ZodLiteral<500>; body: z.ZodType }> {
    if (shape) {
        return httpResponse(500, { body: shape });
    }

    return httpResponse(500, { body: defaultErrorShape });
}

function createRequestSchema<T extends z.ZodRawShape>(
    shape: T,
    params?: z.RawCreateParams,
): ZodHelper.Schema<
    z.ZodObject<{
        body: z.ZodObject<T>;
    }>
> {
    return new ZodHelper.Schema(
        z.object(
            { body: z.object(shape) },
            params,
        ),
    );
}

function createResponseSchema<Types extends readonly [
    z.ZodDiscriminatedUnionOption<"status">,
    ...z.ZodDiscriminatedUnionOption<"status">[]
]>(
    options: Types,
    params?: z.RawCreateParams,
): ZodHelper.Schema<
    z.ZodDiscriminatedUnion<"status", Types>
> {
    return new ZodHelper.Schema(
        z.discriminatedUnion("status", options, params),
    );
}

const prioritySchema = z.number().int();

const signerSchema = z.union([
    z.intersection(
        z.object({
            type: z.literal("corp"),
            priority: prioritySchema,
        }),

        z.union([
            z.object({
                id: z.string(),
            }),

            z.object({
                name: z.string(),
                phone: ZodHelper.employeePhoneSchema,
            }),
        ]),
    ),

    z.object({
        type: z.literal("client"),
        id: z.string().nonempty(),
        name: z.string().nonempty(),
        phone: ZodHelper.clientPhoneSchema,
        priority: prioritySchema,
    }),
]);

export const Schema = {
    corpId: {
        get: {
            request: createRequestSchema({
                login: z.string().nonempty().optional(),
                password: z.string().nonempty().optional(),
            }),

            response: createResponseSchema([
                ok({ corpId }),
                badRequest(),
                internalServerError(),
            ]),
        },
    },

    document: {
        get: {
            request: new ZodHelper.Schema(
                z.object({
                    body: z.object({
                        medialogReference,

                        operatorId,

                        entityType: documentWithUrlType,
                        entityId,
                    }),
                })
                    .transform(data => {
                        if (data.body.entityType === "package") {
                            data.body.entityId = data.body.entityId.toLowerCase();
                        }

                        return data;
                    }),
            ),

            response: createResponseSchema([
                ok({ url }),
                badRequest(),
                internalServerError(),
            ]),
        },

        getStatus: {
            request: createRequestSchema({
                medialogReference,

                entityType: documentWithStatusType,
                entityId,
            }),

            response: createResponseSchema([
                ok({
                    status: z.string(), // TODO
                    documentPackage: z.nullable(
                        z.object({
                            packageId: documentId,
                            packageName: z.string().nonempty(),
                            documents: z.array(
                                z.object({
                                    documentId,
                                    documentName: z.string().nonempty(),
                                    clientSigners: z.array(
                                        z.object({
                                            clientId: z.string(),
                                            clientName: z.string(),
                                            identificationStatus: z.string(),
                                        }),
                                    ),
                                }),
                            ),
                        }),
                    ),
                }),
                badRequest(),
                internalServerError(),
            ]),
        },

        create: {
            request: createRequestSchema({
                operatorId,

                package: z.object({
                    medialogId: medialogId.optional(),

                    id: autogeneratedDocumentId,

                    name: z.string().optional(),
                    objectId: z.string().optional(),
                    needIdentification: ZodHelper.toBoolean,
                    operatorAutoSign: ZodHelper.toBoolean,
                }),

                documents: z.array(
                    z.object({
                        medialogId: medialogId.optional(),

                        id: autogeneratedDocumentId,
                        filePath: z.string().nonempty(),

                        name: z.string().regex(/^.+?\.\w+$/).optional(), // file name with ext
                        unsignExpiredDate: toDate.optional(),
                    }),
                ),

                client: z.object({
                    id: z.string().nonempty(),
                    phone: ZodHelper.clientPhoneSchema,
                    name: z.string().nonempty(),

                    clientRole: z.string().optional(),
                }),

                operator: z.optional(
                    z.object({
                        employeePosition: z.string(),
                    }),
                ),

                payment: z.optional(
                    z.object({
                        packagePaymentLink: z.string().nonempty().optional(),
                        amount: z.number().nonnegative().optional(),
                    }),
                ),
            }),

            response: createResponseSchema([
                ok({ url }),
                badRequest(),
                internalServerError(),
            ]),
        },

        createWithSigners: {
            request: createRequestSchema({
                operatorId,

                addService: ZodHelper.toBoolean,

                package: z.object({
                    medialogId: medialogId.optional(),

                    id: autogeneratedDocumentId,

                    name: z.string().optional(),
                    objectId: z.string().optional(),
                    needIdentification: z.boolean(),
                    identificationType: z.string().optional(),

                    signers: z.array(signerSchema),
                }),

                documents: z.array(
                    z.object({
                        medialogId: medialogId.optional(),

                        id: autogeneratedDocumentId,
                        name: z.string().regex(/^.+?\.\w+$/), // file name with ext
                        filePath: z.string().nonempty(),
                        unsignExpiredDate: toDate.optional(),
                    }),
                ),

                documentTemplates: z.optional(
                    z.array(
                        z.object({
                            id: autogeneratedDocumentId,
                            name: z.string().nonempty().optional(),
                            autoSendToFill: ZodHelper.toBoolean,
                            unsignExpiredDate: toDate.optional(),
                        }),
                    ),
                ),

                payment: z.optional(
                    z.object({
                        packagePaymentLink: z.string().nonempty().optional(),
                        amount: z.number().nonnegative().optional(),
                    }),
                ),
            }),

            response: createResponseSchema([
                ok({ url }),
                badRequest(),
                internalServerError(),
            ]),
        },

        decline: {
            request: createRequestSchema({
                operatorId,

                packageId: documentId,
                declineType: z.string().nonempty().optional(),
            }),

            response: createResponseSchema([
                z.object({
                    status: z.literal(200),
                    body: z.null(),
                }),
                badRequest(),
                internalServerError(),
            ]),
        },

        download: {
            request: createRequestSchema({
                documentId: documentId,
                operatorId: operatorId.optional(),
            }),

            response: createResponseSchema([
                ok({
                    success: z.boolean(),
                    message: z.string(),
                    filesCount: z.number().int().nonnegative().optional(),
                }),
                badRequest(),
                internalServerError(),
            ]),
        },
    },
};

// TODO: optional fields
export const FdocWebhookSchema = {
    setStatus: {
        request: createRequestSchema({
            packageId: uuid,
            documentIds: z.array(z.string()),
            employeeId: z.string().optional(),
            clientId: z.string().optional(),
            actionStatus: z
                .enum([
                    "SIGNED",
                    "CANCELED",
                    "ANNULED",
                    "SIGNED_PAPER",
                    "WAIT_SIGN",
                ])
                .optional(),
            status: z.enum([
                "ANNULED",
                "SIGNED",
                "CANCELED",
                "DELETED",
                "SIGNED_PAPER",
                "WAIT_SIGN",
                "ERROR_ANNUL",
                "ERROR_SEND",
                "ERROR_SIGN",
            ]),
            statusName: z.string().optional(),
            comment: z.string().optional(),
        }),

        response: createResponseSchema([
            ok({}),
            badRequest(),
            internalServerError(),
        ]),
    },

    createDocument: {
        request: new ZodHelper.Schema(
            z.object({
                body: z.intersection(
                    z.object({
                        clients: z.array(
                            z.object({
                                id: z.string().nonempty(),
                                phone: z.string().nonempty(),
                            }),
                        ),
                        packageId: uuid,
                        documentId: uuid,
                        packageName: z.string().nonempty(),
                    }),

                    z.union([
                        z.object({
                            documentType: z.enum([
                                "AGREEMENT",
                                "rejection",
                            ]),
                        }),
                        z.object({
                            documentType: z.literal("ANNUL_AGREEMENT"),
                            annulAgreementInfo: z.object({
                                packageId: uuid,
                                reason: z.string().optional(),
                            }),
                        }),
                    ]),
                ),
            }),
        ),

        response: createResponseSchema([
            ok({}),
            badRequest(),
            internalServerError(),
        ]),
    },
};
