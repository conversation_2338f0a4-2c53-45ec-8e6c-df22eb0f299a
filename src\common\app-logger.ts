/* eslint-disable @typescript-eslint/no-explicit-any */

import type { InspectOptions } from "util";

import { Console } from "console";
import { inspect } from "util";

export class AppLogger extends Console {
    constructor(protected readonly verbose: boolean) {
        super(
            process.stdout,
            process.stderr,
            false,
        );
    }

    override dir(item: any, options?: InspectOptions) {
        if (this.verbose) {
            super.dir(item, { ...options, depth: null, colors: true });
        }
        else {
            super.dir(item, options);
        }
    }

    override debug(...data: unknown[]): void {
        if (this.verbose) {
            super.debug(...data.map(item => {
                if (typeof item === "object") {
                    return inspect(item, { depth: null, colors: true });
                }

                return item;
            }));
        }
    }
}
