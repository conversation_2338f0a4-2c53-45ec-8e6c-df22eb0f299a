export class Token {
    protected readonly _value: string;
    protected readonly _expiresAt: Date;

    constructor(
        value: string,
        expiresAt: Date,
    ) {
        this._value = value;
        this._expiresAt = expiresAt;
    }

    get value() {
        return this.expiresIn > 0 ? this._value : null;
    }

    /** @returns ms */
    get expiresAt() {
        return this._expiresAt.getTime();
    }

    /** @returns ms. */
    get expiresIn() {
        return Math.max(0, this.expiresAt - Date.now());
    }

    isValid(): this is this & { value: string } {
        return this.isValidFor(0);
    }

    isValidFor(ms: number): this is this & { value: string } {
        return this.expiresIn > ms;
    }

    isValidOrError(): asserts this is this & { value: string } {
        if (!this.isValid()) {
            throw new Error("Token expired.", { cause: this });
        }
    }

    isValidForOrError(ms: number): asserts this is this & { value: string } {
        if (!this.isValidFor(ms)) {
            throw new Error("Token expired.", { cause: this });
        }
    }
}
