import type { MedialogService } from "../medialog/service";
import type {
    OperatorToken,
    CreateDto,
    UpdateDto,
    OperatorTokenRepository,
} from "./types";

import { Token } from "../common";

export type Pair = {
    accessToken: Token;
    refreshToken: Token;
}

export class OperatorTokenService {
    constructor(
        protected readonly medialogService: MedialogService,
        protected readonly operatorTokenRepository: OperatorTokenRepository,
    ) {}

    async getPair(operatorId: number): Promise<Pair | null> {
        const operatorToken = await this.operatorTokenRepository.$getOne({ operatorId });

        if (operatorToken) {
            return {
                accessToken: new Token(
                    operatorToken.accessToken,
                    operatorToken.accessTokenExpiresAt,
                ),
                refreshToken: new Token(
                    operatorToken.refreshToken,
                    operatorToken.refreshTokenExpiresAt,
                ),
            };
        }

        return null;
    }

    async create(dto: CreateDto): Promise<OperatorToken> {
        return await this.operatorTokenRepository.$createOne(dto);
    }

    async update(operatorId: number, dto: UpdateDto) {
        return await this.operatorTokenRepository.$update(dto, { operatorId });
    }
}
