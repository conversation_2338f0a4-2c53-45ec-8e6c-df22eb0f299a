import type { MedialogService } from "../medialog";
import type { OperatorTokenService } from "../operator-token";
import type { <PERSON>doc<PERSON><PERSON> } from "./api";

import { Token } from "../common";

export type PasswordCredentials = {
    login: string;
    password: string;
};

type TokenCredentials = {
    refreshToken: string;
};

type Credentials = PasswordCredentials | TokenCredentials;

type DiscriminatedCredentials =
    | ({ type: "basic" } & PasswordCredentials)
    | ({ type: "token" } & TokenCredentials);

type AccessToken = { accessToken: Token };
type RefreshToken = { refreshToken: Token };
type Tokens = AccessToken & RefreshToken;

export const ACCESS_TOKEN_DEFAULT_VALIDITY_MS = 1000 * 60;
export const TOKEN_VALIDITY_MIN_MS = 1000 * 3;

export class FdocAccessTokenService {
    constructor(
        protected readonly api: Fdoc<PERSON><PERSON>,
        protected readonly operatorTokenService: OperatorTokenService,
        protected readonly medialogService: MedialogService,
    ) { }

    protected getDiscriminatedCredentials(credentials: Credentials): DiscriminatedCredentials {
        if ("login" in credentials) {
            return {
                type: "basic",
                login: credentials.login,
                password: credentials.password,
            } as const;
        }

        return {
            type: "token",
            refreshToken: credentials.refreshToken,
        } as const;
    }

    protected authorizeOperator(credentials: PasswordCredentials): Promise<Tokens>;
    protected authorizeOperator(credentials: TokenCredentials): Promise<Tokens>;
    protected async authorizeOperator(credentials: Credentials) {
        const discriminatedCredentials = this.getDiscriminatedCredentials(credentials);

        console.debug("fdocService.authorizeOperator.discriminatedCredentials", discriminatedCredentials);

        const apiResult = await this.api.operatorAccessToken(discriminatedCredentials);

        console.debug("fdocService.authorizeOperator.api.result", apiResult);

        const accessToken = new Token(
            apiResult.accessToken.replace(/^Bearer /, ""),
            new Date(Date.now() + ACCESS_TOKEN_DEFAULT_VALIDITY_MS),
        );

        const refreshToken = new Token(
            apiResult.refreshToken.value,
            apiResult.refreshToken.exp,
        );

        const result = {
            accessToken,
            refreshToken,
        };

        console.debug("fdocService.authorizeOperator.result", result);

        return result;
    }

    protected async getNewTokens(operatorId: number): Promise<Tokens> {
        const credentials = await this.medialogService.getOperatorCredentials(operatorId);

        console.debug("fdocService.getNewTokens.credentials", credentials);

        if (!credentials) {
            throw new Error(`Unknown operator '${operatorId}', no credentials.`);
        }

        const {
            accessToken,
            refreshToken,
        } = await this.authorizeOperator(credentials);

        if (!accessToken.isValidFor(TOKEN_VALIDITY_MIN_MS)) {
            throw new Error("Access token is invalid initially.");
        }

        if (!refreshToken.isValidFor(TOKEN_VALIDITY_MIN_MS)) {
            throw new Error("Refresh token is invalid.");
        }

        return {
            accessToken,
            refreshToken,
        };
    }

    async getAccessToken(operatorId: number): Promise<Token> {
        console.debug("fdocService.getAccessToken.operatorId", operatorId);

        // const localTokens = await this.operatorTokenService.getPair(operatorId);

        // console.debug("fdocService.getAccessToken.localTokens", localTokens);

        // if (!localTokens) {
        //     console.debug("fdocService.getAccessToken.noLocalTokens");

        //     const {
        //         accessToken,
        //         refreshToken,
        //     } = await this.getNewTokens(operatorId);

        //     await this.operatorTokenService.create(
        //         {
        //             operatorId,

        //             accessToken: accessToken.value!,
        //             accessTokenExpiresAt: new Date(accessToken.expiresAt),

        //             refreshToken: refreshToken.value!,
        //             refreshTokenExpiresAt: new Date(refreshToken.expiresAt),
        //         },
        //     );

        //     return accessToken;
        // }

        // if (localTokens.accessToken.isValidFor(TOKEN_VALIDITY_MIN_MS)) {
        //     return localTokens.accessToken;
        // }

        // if (localTokens.refreshToken.isValidFor(TOKEN_VALIDITY_MIN_MS)) {
        //     const {
        //         accessToken,
        //         refreshToken,
        //     } = await this.authorizeOperator({
        //         refreshToken: localTokens.refreshToken.value,
        //     });

        //     if (!accessToken.isValidFor(TOKEN_VALIDITY_MIN_MS)) {
        //         throw new Error("Access token is invalid initially.");
        //     }

        //     if (!refreshToken.isValidFor(TOKEN_VALIDITY_MIN_MS)) {
        //         throw new Error("Refresh token is invalid initially.");
        //     }

        //     await this.operatorTokenService.update(
        //         operatorId,
        //         {
        //             accessToken: accessToken.value,
        //             accessTokenExpiresAt: new Date(accessToken.expiresAt),

        //             refreshToken: refreshToken.value,
        //             refreshTokenExpiresAt: new Date(refreshToken.expiresAt),
        //         },
        //     );

        //     return accessToken;
        // }

        const {
            accessToken,
            // refreshToken,
        } = await this.getNewTokens(operatorId);

        // await this.operatorTokenService.update(
        //     operatorId,
        //     {
        //         accessToken: accessToken.value!,
        //         accessTokenExpiresAt: new Date(accessToken.expiresAt),

        //         refreshToken: refreshToken.value!,
        //         refreshTokenExpiresAt: new Date(refreshToken.expiresAt),
        //     },
        // );

        return accessToken;
    }
}
