import type { z } from "zod";
import type { Assert } from "./create-assert";
import type { Validate } from "./create-validate";
import type { Transform } from "./create-transform";

import { createAssert } from "./create-assert";
import { createValidate } from "./create-validate";
import { createTransform } from "./create-transform";

interface ISchema<T> {
    assert(data: unknown): asserts data is T;
    validate(data: unknown): data is T;
    transform(data: unknown): T;
}

export class Schema<T extends z.ZodType> implements ISchema<z.infer<T>> {
    protected readonly _assert: Assert<z.input<T>>;
    protected readonly _validate: Validate<z.input<T>>;
    protected readonly _transform: Transform<z.output<T>>;

    constructor(readonly schema: T) {
        this._assert = createAssert(schema);
        this._validate = createValidate(schema);
        this._transform = createTransform(schema);
    }

    assert = (data: unknown): asserts data is z.input<T> => {
        this._assert(data);
    };

    validate = (data: unknown): data is z.input<T> => {
        return this._validate(data);
    };

    transform = (data: unknown): z.output<T> => {
        return this._transform(data);
    };
}
