import { z } from "zod";
import { v4 as uuidv4 } from "uuid";
import { Zod<PERSON><PERSON><PERSON> } from "..";

const entityId = z.string().nonempty();
const documentId = z.string().uuid();
const autogeneratedDocumentId = documentId
    .default(() => uuidv4())
    .transform(id => id.toLowerCase());

const documentWithUrlType = z.enum([
    "package",
    "object",
    "client",
    "clientPhone",
    "employee",
    "corporation",
]);

const documentWithStatusType = z.enum([
    "package",
    "document",
]);

const medialogId = z.number().int().positive();

const medialogReference = z
    .object({
        entityType: z.string().nonempty(),
        entityId: medialogId,
    })
    .optional();

const priority = z.number().int();

const operatorId = z.string().nonempty().pipe(z.coerce.number().int());

const signer = z.union([
    z.intersection(
        z.object({
            type: z.literal("corp"),
            priority: priority,
        }),

        z.union([
            z.object({
                id: z.string(),
                // name: z.string().nullable().optional(),
                // phone: z.string().nullable().optional(),
            }),

            z.object({
                // id: z.null().optional(),
                name: z.string(),
                phone: ZodHelper.employeePhoneSchema,
            }),
        ]),
    ),

    z.object({
        type: z.literal("client"),
        id: z.string().nonempty(),
        name: z.string().nonempty(),
        phone: ZodHelper.clientPhoneSchema,
        priority: priority,
    }),
]);

export const Schema = {
    corpId: {
        get: {
            args: z.object({
                login: z.string().nonempty(),
                password: z.string().nonempty(),
            }),

            result: z.object({
                corpId: z.string().nonempty(),
            }),
        },
    },

    document: {
        get: {
            args: z.object({
                medialogReference,

                operatorId,
                entityType: documentWithUrlType,
                entityId,
            }),

            result: z.object({
                url: z.string().url(),
            }),
        },

        getStatus: {
            args: z.object({
                medialogReference,

                entityType: documentWithStatusType,
                entityId: documentId,
            }),

            result: z.object({
                status: z.string(),
                documentPackage: z.nullable(
                    z.object({
                        packageId: z.string().uuid(),
                        packageName: z.string().nonempty(),
                        documents: z.array(
                            z.object({
                                documentId: z.string().uuid(),
                                documentName: z.string().nonempty(),
                                clientSigners: z.array(
                                    z.object({
                                        clientId: z.string(),
                                        clientName: z.string(),
                                        identificationStatus: z.string(),
                                    }),
                                ),
                            }),
                        ),
                    }),
                ),
            }),
        },

        create: {
            args: z.object({
                operatorId,

                package: z.object({
                    medialogId: medialogId.optional(),

                    id: autogeneratedDocumentId,

                    name: z.string().optional(),
                    objectId: z.string().optional(),
                    needIdentification: ZodHelper.toBoolean,
                    operatorAutoSign: ZodHelper.toBoolean,
                }),

                documents: z.array(
                    z.object({
                        medialogId: medialogId.optional(),

                        id: autogeneratedDocumentId,
                        filePath: z.string().nonempty(),

                        name: z.string().regex(/^.+?\.\w+$/).optional(), // file name with ext
                        unsignExpiredDate: ZodHelper.date,
                    }),
                ),

                client: z.object({
                    id: z.string().nonempty(),
                    phone: ZodHelper.clientPhoneSchema,
                    name: z.string().nonempty(),

                    clientRole: z.string().optional(),
                }),

                operator: z.optional(
                    z.object({
                        employeePosition: z.string().optional(),
                    }),
                ),

                payment: z.optional(
                    z.object({
                        packagePaymentLink: z.string().nonempty().optional(),
                        amount: z.string().pipe(z.coerce.number().nonnegative().optional()),
                    }),
                ),
            }),

            result: z.object({
                url: z.string().url(),
            }),
        },

        createWithSigners: {
            args: z.object({
                operatorId,

                addService: ZodHelper.toBoolean,

                package: z.object({
                    medialogId: medialogId.optional(),

                    id: autogeneratedDocumentId,

                    name: z.string().optional(),
                    objectId: z.string().optional(),
                    needIdentification: ZodHelper.toBoolean,
                    identificationType: z.string().optional(),

                    signers: z.array(signer),
                }),

                documents: z.array(
                    z.object({
                        medialogId: medialogId.optional(),

                        id: autogeneratedDocumentId,
                        name: z.string().regex(/^.+?\.\w+$/).optional(), // file name with ext
                        filePath: z.string().nonempty(),
                        unsignExpiredDate: ZodHelper.date,
                    }),
                ),

                documentTemplates: z.array(
                    z.object({
                        id: autogeneratedDocumentId,
                        name: z.string().nonempty().optional(),
                        autoSendToFill: ZodHelper.toBoolean,
                        unsignExpiredDate: ZodHelper.date,
                    }),
                ),

                payment: z.optional(
                    z.object({
                        packagePaymentLink: z.string().nonempty().optional(),
                        amount: z.string().pipe(z.coerce.number().nonnegative().optional()),
                    }),
                ),
            }),

            result: z.object({
                url: z.string().url(),
            }),
        },

        decline: {
            args: z.object({
                operatorId,

                packageId: z.string().uuid(),
                declineType: z.string().nonempty().optional(),
            }),

            result: z.void(),
        },

        syncNewSigned: {
            args: z.object({
                operatorId,
            }),

            result: z.void(),
        },
    },
};
