import type { FdocService } from "../fdoc";

import { AbstractCommand } from "./abstract-command";
import { Schema } from "./schema";

type GetCorpIdArgs = typeof Schema.corpId.get.args._output;
type GetCorpIdResult = typeof Schema.corpId.get.result._output;

export class GetCorpIdCommand extends AbstractCommand<
    typeof Schema.corpId.get.args,
    typeof Schema.corpId.get.result
> {
    constructor(
        protected readonly fdocService: FdocService,
    ) {
        super(
            Schema.corpId.get.args,
            Schema.corpId.get.result,
        );
    }

    override async execute(
        args: GetCorpIdArgs,
    ): Promise<GetCorpIdResult> {
        const corpId = await this.fdocService.getCorpId(args);

        return { corpId };
    }
}

type GetDocumentArgs = typeof Schema.document.get.args._output;
type GetDocumentResult = typeof Schema.document.get.result._output;

export class GetDocumentCommand extends AbstractCommand<
    typeof Schema.document.get.args,
    typeof Schema.document.get.result
> {
    constructor(
        protected readonly fdocService: FdocService,
    ) {
        super(
            Schema.document.get.args,
            Schema.document.get.result,
        );
    }

    override async execute(
        args: GetDocumentArgs,
    ): Promise<GetDocumentResult> {
        return await this.fdocService.getDocument(args);
    }
}

type GetDocumentStatusArgs = typeof Schema.document.getStatus.args._output;
type GetDocumentStatusResult = typeof Schema.document.getStatus.result._output;

export class GetDocumentStatusCommand extends AbstractCommand<
    typeof Schema.document.getStatus.args,
    typeof Schema.document.getStatus.result
> {
    constructor(
        protected readonly fdocService: FdocService,
    ) {
        super(
            Schema.document.getStatus.args,
            Schema.document.getStatus.result,
        );
    }

    override async execute(
        args: GetDocumentStatusArgs,
    ): Promise<GetDocumentStatusResult> {
        return await this.fdocService.getDocumentStatus(args);
    }
}

type CreateDocumentArgs = typeof Schema.document.create.args._output;
type CreateDocumentResult = typeof Schema.document.create.result._output;

export class CreateDocumentCommand extends AbstractCommand<
    typeof Schema.document.create.args,
    typeof Schema.document.create.result
> {
    constructor(
        protected readonly fdocService: FdocService,
    ) {
        super(
            Schema.document.create.args,
            Schema.document.create.result,
        );
    }

    override async execute(
        args: CreateDocumentArgs,
    ): Promise<CreateDocumentResult> {
        return await this.fdocService.createDocument(args);
    }
}

type CreateDocumentWithSignersArgs = typeof Schema.document.createWithSigners.args._output;
type CreateDocumentWithSignersResult = typeof Schema.document.createWithSigners.result._output;

export class CreateDocumentWithSignersCommand extends AbstractCommand<
    typeof Schema.document.createWithSigners.args,
    typeof Schema.document.createWithSigners.result
> {
    constructor(
        protected readonly fdocService: FdocService,
    ) {
        super(
            Schema.document.createWithSigners.args,
            Schema.document.createWithSigners.result,
        );
    }

    override async execute(
        args: CreateDocumentWithSignersArgs,
    ): Promise<CreateDocumentWithSignersResult> {
        return await this.fdocService.createDocumentWithSigners(args);
    }
}

type DeclineDocumentArgs = typeof Schema.document.decline.args._output;
type DeclineDocumentResult = typeof Schema.document.decline.result._output;

export class DeclineDocumentCommand extends AbstractCommand<
    typeof Schema.document.decline.args,
    typeof Schema.document.decline.result
> {
    constructor(
        protected readonly fdocService: FdocService,
    ) {
        super(
            Schema.document.decline.args,
            Schema.document.decline.result,
        );
    }

    override async execute(
        args: DeclineDocumentArgs,
    ): Promise<DeclineDocumentResult> {
        return await this.fdocService.declineDocument({
            operatorId: args.operatorId,
            packageGuid: args.packageId,
            declineType: args.declineType ?? null,
        });
    }
}

type SyncNewSignedDocumentsArgs = typeof Schema.document.syncNewSigned.args._output;

export class SyncNewSignedDocumentsCommand extends AbstractCommand<
    typeof Schema.document.syncNewSigned.args,
    typeof Schema.document.syncNewSigned.result
> {
    constructor(
        protected readonly fdocService: FdocService,
    ) {
        super(
            Schema.document.syncNewSigned.args,
            Schema.document.syncNewSigned.result,
        );
    }

    override async execute(
        args: SyncNewSignedDocumentsArgs,
    ): Promise<void> {
        await this.fdocService.syncNewSignedDocuments(args);
    }
}
