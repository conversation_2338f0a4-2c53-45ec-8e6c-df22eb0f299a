import type { ZodIssue } from "zod";

import { ZodIssueCode } from "zod";

export function transformIssues(issues: ZodIssue[]) {
    const result: unknown[] = [];

    for (const issue of issues) {
        switch (issue.code) {
            case ZodIssueCode.invalid_union: {
                const { code, path, unionErrors, ..._issue } = issue;

                result.push({
                    code,
                    path,
                    ..._issue,
                    issues: unionErrors.map(e => transformIssues(e.issues)),
                });

                break;
            }

            case ZodIssueCode.invalid_arguments: {
                const { code, path, argumentsError, ..._issue } = issue;

                result.push({
                    code,
                    path,
                    ..._issue,
                    issues: transformIssues(argumentsError.issues),
                });

                break;
            }

            case ZodIssueCode.invalid_return_type: {
                const { code, path, returnTypeError, ..._issue } = issue;

                result.push({
                    code,
                    path,
                    ..._issue,
                    issues: transformIssues(returnTypeError.issues),
                });

                break;
            }

            default: {
                const { code, path, ..._issue } = issue;

                result.push({
                    code,
                    path,
                    ..._issue,
                });

                break;
            }
        }
    }

    return result;
}
