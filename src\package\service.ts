import type {
    Package,
    CreateDto,
    UpdateDto,
    PackageRepository,
} from "./types";

export class PackageService {
    constructor(
        protected readonly packageRepository: PackageRepository,
    ) {}

    async isExists(fdocPackageId: string): Promise<boolean> {
        return await this.packageRepository.$getOne({ fdocPackageId }) !== null;
    }

    async create(dto: CreateDto): Promise<Package> {
        return await this.packageRepository.$createOne(dto);
    }

    async getOne(fdocPackageId: string): Promise<Package | null> {
        return await this.packageRepository.$getOne({ fdocPackageId });
    }

    async update(fdocPackageId: string, dto: UpdateDto) {
        return await this.packageRepository.$update(dto, { fdocPackageId });
    }
}
