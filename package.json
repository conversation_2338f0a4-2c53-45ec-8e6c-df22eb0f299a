{"name": "fdoc", "version": "0.2.0", "description": "FDoc API implementation", "main": "./src/cli.ts", "type": "module", "scripts": {"cli:dev": "tsx src/cli.ts", "cli": "node out/cli.mjs"}, "bin": {"fdoc": "out/cli.mjs"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@itmedsib/config": "^1.6.0", "@itmedsib/medialog": "^0.1.2", "argparse": "^2.0.1", "axios": "^1.7.9", "body-parser": "^1.20.3", "commander": "^13.1.0", "execa": "^7.2.0", "express": "^4.21.2", "knex": "^3.1.0", "medialog": "file:medialog-1.2.0.tgz", "minimist": "^1.2.8", "objection": "^3.1.5", "pg": "^8.13.1", "samba-client": "^7.2.0", "uuid": "^11.1.0", "yargs": "^17.7.2", "zod": "^3.24.1"}, "devDependencies": {"@stylistic/eslint-plugin-js": "^3.1.0", "@tsconfig/strictest": "^2.0.5", "@types/argparse": "^2.0.17", "@types/express": "^5.0.0", "@types/minimist": "^1.2.5", "@types/node": "^22.13.1", "@types/yargs": "^17.0.33", "@typescript-eslint/eslint-plugin": "^8.24.0", "@typescript-eslint/parser": "^8.24.0", "eslint": "^8.57.1", "tsup": "^8.3.6", "typescript": "^5.7.3"}}