import type { AxiosRequestConfig, AxiosResponse } from "axios";

import querystring from "node:querystring";

import axios, { isAxiosError } from "axios";

type HttpTransportConfig = Partial<{
    /**
     * Base url (domain, domain + some path). Applied to relative URLs.
     * @example "https://google.com"
     * @default ""
     */
    baseUrl: string;

    /**
     * Time until timeout error (ms).
     * @example 5000 - 5 seconds
     * @default 0
     */
    timeout: number;

    /**
     * Number of retries in case of timeout error. Another errors bubble.
     * @example 3
     * @default 0 - infinite retries (until non-timeout error)
     */
    retries: number;

    /**
     * Method of request.
     * @default "get"
     */
    method: "get" | "post" | "put" | "patch" | "delete";

    /**
     * Headers of request.
     * @example { "Authorization": "Bearer ..." }
     * @default {}
     */
    headers: Record<string, string>;

    /**
     * Config for other axios request params.
     */
    axios: Normalize<
        Omit<
            AxiosRequestConfig,
            | "baseURL"
            | "timeout"
            | "method"
            | "headers"
            | "url"
            | "data"
            | "timeoutErrorMessage"
        >
    >;
}>;

type Result<T> =
    | {
        success: true;
        status: number;
        response: T;
        headers: AxiosResponse["headers"];
    }
    | {
        success: false;
        status: number;
        response: string;
    };

export class HttpTransport {
    constructor(private readonly defaultConfig: HttpTransportConfig = {}) { }

    async get(
        url: string,
        params?: Record<string, string> | null | undefined,
        config: Normalize<Omit<HttpTransportConfig, "method">> = {},
    ) {
        if (params) {
            const stringifiedParams = querystring.stringify(params);

            if (stringifiedParams.length) {
                url += `?${stringifiedParams}`;
            }
        }

        return await this.send(
            url,
            null,
            {
                ...config,
                method: "get",
            },
        );
    }

    async post(
        url: string,
        params?: Record<string, string> | null | undefined,
        body?: unknown,
        config: Normalize<Omit<HttpTransportConfig, "method">> = {},
    ) {
        if (params) {
            const stringifiedParams = querystring.stringify(params);

            if (stringifiedParams.length) {
                url += `?${stringifiedParams}`;
            }
        }

        return await this.send(
            url,
            body ?? null,
            {
                ...config,
                method: "post",
            },
        );
    }

    /**
     * Sends data to a given url and returns string response.
     * @example
     * {
     *   success: true,
     *   status: 200,
     *   response: "<content>qwertyuiop</content>"
     *   headers: { ... }
     * }
     */
    async send<T = string>(
        url: string,
        data: unknown,
        config: HttpTransportConfig = {},
    ): Promise<Result<T>> {
        const {
            method = "get",
            baseUrl = "",
            timeout = 0,
            retries = 0,
            headers,
            axios: axiosConfig = {},
        }: HttpTransportConfig = { ...this.defaultConfig, ...config };

        const axiosHeaders: Record<string, string> = Object.assign(
            {},
            this.defaultConfig.headers,
            headers,
        );

        const requestData: AxiosRequestConfig = {
            responseType: "text",

            ...axiosConfig,

            headers: axiosHeaders,

            method,
            timeout,
            baseURL: baseUrl,

            url,
            data,
        };

        if (global.debugTransport) {
            console.debug({ requestData });
        }

        for (let i = 1; ; i++) {
            try {
                try {
                    const axiosResponse = await axios(requestData);

                    if (global.debugTransport) {
                        console.debug({ url, axiosResponse });
                    }

                    return {
                        success: true,
                        status: axiosResponse.status,
                        response: axiosResponse.data as T,
                        headers: axiosResponse.headers,
                    };
                }
                catch (e) {
                    if (isAxiosError(e) && e.response) return {
                        success: false,
                        status: e.response.status,
                        response: e.response.data as string,
                    };

                    throw e;
                }
            }
            catch (e) {
                if (isAxiosError(e) && (e.code === "ECONNABORTED" || e.code === "ETIMEDOUT")) {
                    if (retries === 0 || i < retries) continue;
                }

                throw e;
            }
        }
    }
}
