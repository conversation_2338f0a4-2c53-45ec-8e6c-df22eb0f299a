import type { Options } from "execa";

import path from "path";
import { tmpdir } from "os";
import SambaClient from "samba-client";
import { execa } from "execa";

export type SmbClientOptions = Normalize<ConstructorParameters<typeof SambaClient>[0]>

export class SmbService {
    protected sambaClient: SambaClient;

    constructor(
        protected readonly options: SmbClientOptions,
    ) {
        this.sambaClient = new SambaClient(options);
    }

    async getFile(
        path: string,
        destination: string,
    ) {
        const result = await execa(
            "smbclient", [
                this.options.address,
                "-U",
                String(this.options.username),
                "--password",
                String(this.options.password),
                "-W",
                String(this.options.domain),
            ],
            {
                input: `get "${path}" "${destination}"`,
            } satisfies Options,
        );

        if (result.exitCode !== 0) {
            console.log(result.stdout);

            throw new Error(`Failed to get file ${path} to ${destination}: ${result.stderr}`);
        }

    }

    async downloadAssMedSignedFiles(dto: {
        year: number;
        month: number;
        day: number;
        packageId: string;
        documentId: string;
    }) {
        console.debug(
            "[assmed-smb] searching for signed files",
            dto,
        );

        try {
            const datedRoot = path.win32.join(
                dto.year.toString(),
                dto.month.toString(),
                dto.day.toString(),
                path.win32.sep,
            );

            const packageFolders = await this.sambaClient.list(datedRoot);

            const packageIdLowerCase = dto.packageId.toLowerCase();

            console.debug(
                "[assmed-smb] available packages",
                {
                    datedRoot,
                    packageFolders,
                    packageIdLowerCase,
                },
            );

            for (const packageFolder of packageFolders) {
                if (!packageFolder.name.toLowerCase().startsWith(packageIdLowerCase)) {
                    continue;
                }

                let fileDocumentId: string | null = null;

                if (
                    await this.sambaClient.fileExists(
                        path.win32.join(
                            datedRoot,
                            packageFolder.name,
                            dto.documentId.toLowerCase(),
                            `${dto.documentId.toLowerCase()}.pdf`,
                        ),
                    )
                ) {
                    fileDocumentId = dto.documentId.toLowerCase();
                }
                else if (
                    await this.sambaClient.fileExists(
                        path.win32.join(
                            datedRoot,
                            packageFolder.name,
                            dto.documentId.toUpperCase(),
                            `${dto.documentId.toUpperCase()}.pdf`,
                        ),
                    )
                ) {
                    fileDocumentId = dto.documentId.toUpperCase();
                }
                else {
                    throw new Error(`Document ${dto.documentId} not found in package folder ${packageFolder.name}`);
                }

                const folder = path.win32
                    .join(
                        datedRoot,
                        packageFolder.name,
                        fileDocumentId,
                    );

                console.debug(
                    "[assmed-smb] found package",
                    {
                        folderName: packageFolder.name,
                        fileDocumentId,
                        folder,
                    },
                );

                const tmpPdfPath = path.join(tmpdir(), `${dto.documentId}.pdf`);
                const tmpPdfSigPath = path.join(tmpdir(), `${dto.documentId}.pdf.sig`);
                const tmpPrintedFormPath = path.join(tmpdir(), `${dto.documentId}_printed_form.pdf`);

                console.debug(
                    "[assmed-smb] downloading files",
                    {
                        tmpPdfPath,
                        tmpPdfSigPath,
                        tmpPrintedFormPath,
                    },
                );

                await Promise.all([
                    this.getFile(
                        path.win32.join(folder, `${fileDocumentId}.pdf`),
                        tmpPdfPath,
                    ),

                    this.getFile(
                        path.win32.join(folder, `${fileDocumentId}.pdf.sig`),
                        tmpPdfSigPath,
                    ),

                    this.getFile(
                        path.win32.join(folder, `${fileDocumentId}_printed_form.pdf`),
                        tmpPrintedFormPath,
                    ),
                ]);

                return {
                    pdf: tmpPdfPath,
                    pdfSig: tmpPdfSigPath,
                    printedForm: tmpPrintedFormPath,
                };
            }
        }
        catch (e) {
            console.debug(e);

            return null;
        }

        return null;
    }
}
