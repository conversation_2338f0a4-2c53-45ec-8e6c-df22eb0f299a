/** @param { import("knex").Knex } knex */
export function up(knex) {
    return knex.schema

        .createTable("operator_tokens", table => {
            table.increments();
            table.timestamps();

            table.integer("operator_id");

            table.text("access_token");
            table.timestamp("access_token_expires_at", { useTz: true });

            table.string("refresh_token");
            table.timestamp("refresh_token_expires_at", { useTz: true });
        });
}

/** @param { import("knex").Knex } knex */
export function down(knex) {
    return knex.schema

        .dropTable("operator_tokens");
}
