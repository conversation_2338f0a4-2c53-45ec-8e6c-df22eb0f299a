/** @param { import("knex").Knex } knex */
export async function up(knex) {
    return knex.schema
        .alterTable("documents", table => {
            table.text("file_path").nullable().alter();
        });
}

/** @param { import("knex").Knex } knex */
export function down(knex) {
    return knex.schema
        .alterTable("documents", table => {
            table.text("file_path").notNullable();
        });
}
