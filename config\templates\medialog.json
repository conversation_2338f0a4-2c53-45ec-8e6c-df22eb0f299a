{
  "medialog": {
    "database": {
      "client": "mssql",
      "connection": {
        "host": "$(medialog_db_host)",
        "port": $(medialog_db_port),
        "database": "$(medialog_db_name)",
        "user": "$(medialog_db_user)",
        "password": "$(medialog_db_password)"
      },
      "pool": {
        "min": $(medialog_pool_min ?? 0),
        "max": $(medialog_pool_max ?? 10),
        "idleTimeoutMillis": $(medialog_pool_idle_timeout_millis ?? 1000)
      }
    },
    "queriesPath": "$(medialog_queries_path ?? queries)",
    "smb": {
      "address": "$(medialog_smb_address)",
      "username": "$(medialog_smb_username)",
      "password": "$(medialog_smb_password)",
      "domain": "$(medialog_smb_domain)",

      "assmed": {
        "signedDocumentsRoot": "$(smb_assmed_signed_documents_root)"
      }
    }
  }
}