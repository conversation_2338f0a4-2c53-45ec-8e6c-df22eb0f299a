import type { SmbService } from "../../smb/service";
import type { FdocAccessTokenService } from "../access-token-service";
import type { FdocApi } from "../api";
import type { SignedDocumentAdapter } from "./adapter";

import { AssMedAdapter } from "./assmed";
import { FdocAdapter } from "./fdoc";

export class SignedDocumentAdapterFactory {
    constructor(
        protected readonly fdocApi: FdocApi,
        protected readonly fdocAccessTokenService: FdocAccessTokenService,
        protected readonly smbService: SmbService,
    ) { }

    create(adapter: "fdoc" | "assmed"): SignedDocumentAdapter {
        switch (adapter) {
            case "fdoc":
                return new FdocAdapter(this.fdocApi, this.fdocAccessTokenService);

            case "assmed":
                return new AssMedAdapter(this.smbService);
        }
    }
}
