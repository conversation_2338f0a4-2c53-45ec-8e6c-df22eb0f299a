/** @param { import("knex").Knex } knex */
export async function up(knex) {
    await knex.schema

        .alterTable("documents", table => {
            table.text("medialog_file_path");
            table.boolean("is_downloaded").notNullable().defaultTo(false);
        });

    const documentsWithMedialogSchema = await knex.table("documents")
        .whereLike("file_path", "medialog:%");

    await Promise.all(documentsWithMedialogSchema.map(async document => {
        const medialogFilePath = document.file_path
            .replace(/^medialog:\/\//, "")
            .replace(/^medialog:/, "");

        await knex.table("documents")
            .where({ id: document.id })
            .update({
                medialog_file_path: medialogFilePath,
            });
    }));
}

/** @param { import("knex").Knex } knex */
export function down(knex) {
    return knex.schema

        .alterTable("documents", table => {
            table.dropColumn("medialog_file_path");
            table.dropColumn("is_downloaded");
        });
}
