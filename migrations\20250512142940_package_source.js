/** @param { import("knex").Knex } knex */
export async function up(knex) {
    return knex.schema
        .alterTable("packages", table => {
            table.text("source");
        })
        .then(() => knex.raw(`
            UPDATE packages 
            SET source = CASE 
                WHEN EXISTS (
                    SELECT 1 FROM documents 
                    WHERE documents.fdoc_package_id = packages.fdoc_package_id 
                    AND documents.file_path IS NOT NULL
                ) THEN 'medialog'
                ELSE 'fdoc'
            END
            WHERE source IS NULL
        `));
}

/** @param { import("knex").Knex } knex */
export function down(knex) {
    return knex.schema
        .alterTable("packages", table => {
            table.dropColumn("source");
        });
}
