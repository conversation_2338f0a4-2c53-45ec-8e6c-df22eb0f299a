{"fdoc": {"auth": {"login": "$(fdoc_auth_login)", "password": "$(fdoc_auth_password)", "appCode": "$(fdoc_auth_app_code)", "apiKey": "$(fdoc_auth_api_key)", "corpId": "$(fdoc_auth_corp_id)"}, "url": {"base": "$(fdoc_url_base ?? https://demo.fdoc.ru/opengate/api/v1)", "archivesBase": "$(fdoc_url_archives_base ?? https://demo.fdoc.ru/archive-srv/api/v1)"}, "signedDocuments": {"bes": {"enabled": "$(fdoc_signed_documents_bes_enabled ?? false)", "provider": "$(fdoc_signed_documents_bes_provider ?? fdoc)"}, "qes": {"enabled": "$(fdoc_signed_documents_qes_enabled ?? false)", "provider": "$(fdoc_signed_documents_qes_provider ?? fdoc)"}}}}