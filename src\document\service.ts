import type {
    Document,
    CreateDto,
    DocumentRepository,
    UpdateDto,
} from "./types";

export class DocumentService {
    constructor(
        protected readonly documentRepository: DocumentRepository,
    ) { }

    async isExists(fdocDocumentId: string): Promise<boolean> {
        return await this.documentRepository.$getOne({ fdocDocumentId }) !== null;
    }

    async createOne(dto: CreateDto): Promise<Document> {
        return await this.documentRepository.$createOne(dto);
    }

    async createMany(dtos: CreateDto[]): Promise<Document[]> {
        return await this.documentRepository.$createMany(dtos);
    }

    async getOne(fdocDocumentId: string): Promise<Document | null> {
        return await this.documentRepository.$getOne({ fdocDocumentId });
    }

    async getMany(dto: {
        isDownloaded?: boolean;
    }): Promise<Document[]> {
        return await this.documentRepository.$getMany(dto);
    }

    async getSignedButNotDownloaded() {
        return await this.documentRepository.getSignedButNotDownloaded();
    }

    async update(id: number, dto: UpdateDto) {
        return await this.documentRepository.$update(dto, { id });
    }
}
