import type { SyncoRepository } from "../repository";
import type { Synco } from "../types";

import { SyncoModel } from "./model";

export class DbSyncoRepository implements SyncoRepository {
    async create(name: string): Promise<Synco> {
        return await SyncoModel
            .query()
            .insert({
                name,
                success: null,
            });
    }

    async find(name: string, success: boolean): Promise<Synco | undefined> {
        return await SyncoModel
            .query()
            .where({ name, success })
            .orderBy("updatedAt", "DESC")
            .first();
    }

    async update(id: number, success: boolean): Promise<void> {
        await SyncoModel
            .query()
            .where({ id })
            .update({ success });
    }
}
