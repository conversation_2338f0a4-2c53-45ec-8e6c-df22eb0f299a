import type { SyncoRepository } from "./repository";

export class SyncoService {
    constructor(
        protected readonly repository: SyncoRepository,
    ) { }

    async create(name: string) {
        return await this.repository.create(name);
    }

    async find(name: string, success = true) {
        return await this.repository.find(name, success);
    }

    async update(id: number, success: boolean) {
        return await this.repository.update(id, success);
    }

    async withSynco(name: string, action: (lastSuccess: Date) => Promise<boolean | void>) {
        const lastSuccessSynco = await this.find(name, true);
        const synco = await this.create(name);

        await action(lastSuccessSynco?.createdAt ?? new Date())
            .then(result => this.update(synco.id, result ?? true))
            .catch(e => (
                console.error(e),
                this.update(synco.id, false)
            ));
    }
}
