import type { NextFunction, Request, Response } from "express";
import type { FdocService } from "../fdoc";
import type { MedialogService } from "../medialog";
import type { PackageService } from "../package";

import express from "express";
import bodyParser from "body-parser";
import { configProvider } from "../config";
import { z, <PERSON>od<PERSON><PERSON><PERSON> } from "..";
import * as Hand<PERSON> from "./handler";

export class ServerService {
    constructor(
        protected readonly fdocService: FdocService,
        protected readonly medialogService: MedialogService,
        protected readonly packageService: PackageService,
    ) {
        this.handleError = this.handleError.bind(this);
        this.logRoute = this.logRoute.bind(this);
    }

    protected handleError(
        req: Request,
        res: Response,
        e: unknown,
        statusCode = 500,
    ) {
        console.error(`Error in ${req.method} ${req.path}:`);

        let body: unknown = null;

        if (e instanceof z.ZodError) {
            const issues = <PERSON>odHelper.transformIssues(e.issues);

            console.debug(issues);

            body = {
                message: "Not passed validation.",
                issues,
            };
        }
        else {
            body = {
                message: e instanceof Error ? e.message : String(e),
            };
        }

        if (debug) {
            res
                .status(statusCode)
                .send(body);
        }
        else {
            res.sendStatus(statusCode);
        }
    }

    protected warnAboutNoValidation(
        req: Request,
        _: Response,
        next: NextFunction,
    ) {
        console.warn(`${req.method} ${req.path} - NO REQUEST/RESPONSE DATA VALIDATION`);

        next();
    }

    protected logRoute(
        req: Request,
        _: Response,
        next: NextFunction,
    ) {
        console.debug(`serverService.${req.path}.params`, req.params);
        console.debug(`serverService.${req.path}.query`, req.query);
        console.debug(`serverService.${req.path}.body`, req.body);

        next();
    }

    run(port: number) {
        const app = express();

        app.use(bodyParser.urlencoded({ extended: true }));
        app.use(bodyParser.json());

        // app.use("*", this.warnAboutNoValidation);
        app.use("*", this.logRoute);

        app.get("/ping", (_, res) => {
            res.send("pong");
        });

        app.post("/ping", (req, res) => {
            res.send("pong " + JSON.stringify(req.body));
        });

        {
            const fdocWebhookHandler = new Handler
                .FdocWebhookHandler(
                    this.fdocService,
                    this.medialogService,
                    this.packageService,
                );

            app.use(
                "/webhook/fdoc",
                fdocWebhookHandler.getHandler(),
            );

            const credentials = {
                login: configProvider.get("fdoc:auth:login"),
                password: configProvider.get("fdoc:auth:password"),
            };

            fdocWebhookHandler
                .initExternalWebhookRegistry(credentials)
                .catch((e) => {
                    console.error("Unable to (re-)register FDoc webhooks:");
                    console.error(e);
                });
        }

        app.get(
            "/corp-id",
            new Handler
                .GetCorpIdHandler(this.fdocService)
                .handler,
        );

        app.get(
            "/document",
            new Handler
                .GetDocumentHandler(this.fdocService)
                .handler,
        );

        app.get(
            "/document-status",
            new Handler
                .GetDocumentStatusHandler(this.fdocService)
                .handler,
        );

        app.post(
            "/document",
            new Handler
                .CreateDocumentHandler(this.fdocService)
                .handler,
        );

        app.post(
            "/document-with-signers",
            new Handler
                .CreateDocumentWithSignersHandler(this.fdocService)
                .handler,
        );

        app.post(
            "/decline-document",
            new Handler
                .DeclineDocumentHandler(this.fdocService)
                .handler,
        );

        app.listen(port, (error) => {
            if (error) {
                console.error(error);

                process.exit(1);
            }

            console.log(`Server is listening at port ${port}...`);
        });

        process.once("SIGINT", () => process.exit(0));
        process.once("SIGTERM", () => process.exit(0));
    }
}
