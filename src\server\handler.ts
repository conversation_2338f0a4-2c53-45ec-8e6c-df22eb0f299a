import type { z } from "..";
import type { FdocService } from "../fdoc";
import type { MedialogService } from "../medialog";
import type { PackageService } from "../package";

import { configProvider } from "../config";
import { Abstract<PERSON>and<PERSON> } from "./abstract-handler";
import { FdocWebhookSchema, Schema } from "./schema";
import { Router } from "express";

export type GetCorpIdParams = z.output<typeof Schema.corpId.get.request.schema>;
export type GetCorpIdResult = z.output<typeof Schema.corpId.get.response.schema>;

export class GetCorpIdHandler extends AbstractHandler {
    constructor(protected readonly fdocService: FdocService) {
        super(
            Schema.corpId.get.request.transform,
            Schema.corpId.get.response.assert,
        );
    }

    override async execute(
        params: GetCorpIdParams,
    ): Promise<GetCorpIdResult> {
        const { body } = params;

        const credentials = {
            login: configProvider.get("fdoc:auth:login"),
            password: configProvider.get("fdoc:auth:password"),
            ...body,
        };

        const corpId = await this.fdocService.getCorpId(credentials);

        return {
            status: 200,
            body: {
                corpId,
            },
        };
    }
}

export type GetDocumentParams = z.output<typeof Schema.document.get.request.schema>;
export type GetDocumentResult = z.output<typeof Schema.document.get.response.schema>;

export class GetDocumentHandler extends AbstractHandler {
    constructor(protected readonly fdocService: FdocService) {
        super(
            Schema.document.get.request.transform,
            Schema.document.get.response.assert,
        );
    }

    override async execute(
        params: GetDocumentParams,
    ): Promise<GetDocumentResult> {
        const { body } = params;

        const document = await this.fdocService.getDocument(body);

        return {
            status: 200,
            body: document,
        };
    }
}

export type GetDocumentStatusParams = z.output<typeof Schema.document.getStatus.request.schema>;
export type GetDocumentStatusResult = z.output<typeof Schema.document.getStatus.response.schema>;

export class GetDocumentStatusHandler extends AbstractHandler {
    constructor(
        protected readonly fdocService: FdocService,
    ) {
        super(
            Schema.document.getStatus.request.transform,
            Schema.document.getStatus.response.assert,
        );
    }

    override async execute(
        params: GetDocumentStatusParams,
    ): Promise<GetDocumentStatusResult> {
        const { body } = params;

        const document = await this.fdocService.getDocumentStatus(body);

        return {
            status: 200,
            body: document,
        };
    }
}

export type CreateDocumentParams = z.output<typeof Schema.document.create.request.schema>;
export type CreateDocumentResult = z.output<typeof Schema.document.create.response.schema>;

export class CreateDocumentHandler extends AbstractHandler {
    constructor(
        protected readonly fdocService: FdocService,
    ) {
        super(
            Schema.document.create.request.transform,
            Schema.document.create.response.assert,
        );
    }

    override async execute(
        params: CreateDocumentParams,
    ): Promise<CreateDocumentResult> {
        const { body } = params;

        const document = await this.fdocService.createDocument(body);

        return {
            status: 200,
            body: document,
        };
    }
}

export type CreateDocumentWithSignersParams = z.output<typeof Schema.document.createWithSigners.request.schema>;
export type CreateDocumentWithSignersResult = z.output<typeof Schema.document.createWithSigners.response.schema>;

export class CreateDocumentWithSignersHandler extends AbstractHandler {
    constructor(
        protected readonly fdocService: FdocService,
    ) {
        super(
            Schema.document.createWithSigners.request.transform,
            Schema.document.createWithSigners.response.assert,
        );
    }

    override async execute(
        params: CreateDocumentWithSignersParams,
    ): Promise<CreateDocumentWithSignersResult> {
        const { body } = params;

        const document = await this.fdocService.createDocumentWithSigners({
            ...body,
            documentTemplates: body.documentTemplates ?? [],
        });

        return {
            status: 200,
            body: document,
        };
    }
}

export type DeclineDocumentParams = z.output<typeof Schema.document.decline.request.schema>;
export type DeclineDocumentResult = z.output<typeof Schema.document.decline.response.schema>;

export class DeclineDocumentHandler extends AbstractHandler {
    constructor(protected readonly fdocService: FdocService) {
        super(
            Schema.document.decline.request.transform,
            Schema.document.decline.response.assert,
        );
    }

    override async execute(
        params: DeclineDocumentParams,
    ): Promise<DeclineDocumentResult> {
        const { body } = params;

        await this.fdocService.declineDocument({
            operatorId: body.operatorId,
            packageGuid: body.packageId,
            declineType: body.declineType ?? null,
        });

        return {
            status: 200,
            body: null,
        };
    }
}

export class FdocWebhookHandler {
    constructor(
        protected readonly fdocService: FdocService,
        protected readonly medialogService: MedialogService,
        protected readonly packageService: PackageService,
    ) { }

    async initExternalWebhookRegistry(params: {
        login: string;
        password: string;
    }) {
        await Promise.all([
            this.fdocService.createWebhook({
                login: params.login,
                password: params.password,

                events: [
                    {
                        url: configProvider.get("server:externalAddress") + "/webhook/fdoc/set-status",
                        code: "SetStatus",
                    },
                ],
            }),

            this.fdocService.createWebhook({
                login: params.login,
                password: params.password,

                events: [
                    {
                        url: configProvider.get("server:externalAddress") + "/webhook/fdoc/create-document",
                        code: "CreateDocument",
                    },
                ],
            }),
        ]);
    }

    getHandler() {
        const router = Router();

        router.post(
            "/set-status",
            new FdocWebhookSetStatusHandler(
                this.fdocService,
                this.medialogService,
                this.packageService,
            ).handler,
        );

        router.post(
            "/create-document",
            new FdocWebhookCreateDocumentHandler(
                this.fdocService,
            ).handler,
        );

        return router;
    }
}

export type FdocWebhookSetStatusParams = z.output<typeof FdocWebhookSchema.setStatus.request.schema>;
export type FdocWebhookSetStatusResult = z.output<typeof FdocWebhookSchema.setStatus.response.schema>;

export class FdocWebhookSetStatusHandler extends AbstractHandler {
    constructor(
        protected readonly fdocService: FdocService,
        protected readonly medialogService: MedialogService,
        protected readonly packageService: PackageService,
    ) {
        super(
            FdocWebhookSchema.setStatus.request.transform,
        );
    }

    override async execute(
        params: FdocWebhookSetStatusParams,
    ): Promise<FdocWebhookSetStatusResult> {
        const { body } = params;

        await this.packageService.update(body.packageId, {
            status: body.status,
        });

        await this.medialogService.updatePackageStatus(
            body.packageId,
            body.status,
        );

        return {
            status: 200,
            body: {},
        };
    }
}

export type FdocWebhookCreateDocumentParams = z.output<typeof FdocWebhookSchema.createDocument.request.schema>;
export type FdocWebhookCreateDocumentResult = z.output<typeof FdocWebhookSchema.createDocument.response.schema>;

export class FdocWebhookCreateDocumentHandler extends AbstractHandler {
    constructor(
        protected readonly fdocService: FdocService,
    ) {
        super(
            FdocWebhookSchema.createDocument.request.transform,
        );
    }

    override async execute(
        params: FdocWebhookCreateDocumentParams,
    ): Promise<FdocWebhookCreateDocumentResult> {
        const { body } = params;

        await this.fdocService.handleCreateDocument(body);

        return {
            status: 200,
            body: {},
        };
    }
}

export type DownloadDocumentParams = z.output<typeof Schema.document.download.request.schema>;
export type DownloadDocumentResult = z.output<typeof Schema.document.download.response.schema>;

export class DownloadDocumentHandler extends AbstractHandler {
    constructor(protected readonly fdocService: FdocService) {
        super(
            Schema.document.download.request.transform,
            Schema.document.download.response.assert,
        );
    }

    override async execute(
        params: DownloadDocumentParams,
    ): Promise<DownloadDocumentResult> {
        const { body } = params;

        const result = await this.fdocService.downloadDocumentById({
            documentId: body.documentId,
            operatorId: body.operatorId,
        });

        return {
            status: 200,
            body: result,
        };
    }
}
