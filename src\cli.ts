/* eslint-disable no-inner-declarations */

import type {
    CreateDocumentCliParams,
    CreateDocumentWithSignersCliParams,
    GetCorpIdCliParams,
    GetDocumentCliParams,
    GetDocumentStatusCliParams,
    DeclineDocumentCliParams,
    SyncNewSignedDocumentsCliParams,
} from "./cli/index";

interface SmbGetFileOptions {
    address: string;
    username: string;
    password: string;
    domain: string;
    path: string;
    destination: string;
}

// import fs from "fs";
import path from "path";
import { Command /* , createOption, Option */ } from "commander";
import { ZodError } from "zod";
import { configProvider } from "./config";
import { AppLogger } from "./common";
import { HttpTransport } from "./transport";
import { transformIssues } from "./zod-helper";
// import { DbSyncoRepository, SyncoService } from "./synco";
// import { ConcurrentRunner, groupIndexedArgs, parseNumber, sleep } from "./utils";
import { FdocService } from "./fdoc/service";
import { FdocApi } from "./fdoc/api";
import { CliService } from "./cli/index";
import { MedialogService } from "./medialog/service";
import { OperatorTokenDatabaseRepository, OperatorTokenService } from "./operator-token";
import { ServerService } from "./server";
import { PackageDatabaseRepository } from "./package";
import { PackageService } from "./package";
import { DocumentDatabaseRepository } from "./document";
import { DocumentService } from "./document";
import { SmbService } from "./smb";
import { FdocAccessTokenService } from "./fdoc/access-token-service";

const argv = process.argv.slice(2);
global.debug = argv.includes("--debug");
global.debugTransport = argv.includes("--debug-transport");

const medialogService = new MedialogService(
    path.join(process.cwd(), configProvider.get("medialog:queriesPath")),
    configProvider.get("medialog:database"),
);

const operatorTokenService = new OperatorTokenService(
    medialogService,
    new OperatorTokenDatabaseRepository(),
);

const packageService = new PackageService(new PackageDatabaseRepository());
const documentService = new DocumentService(new DocumentDatabaseRepository());
const smbService = new SmbService({
    ...configProvider.get("medialog:smb"),
    address: configProvider.get("medialog:smb:assmed:signedDocumentsRoot"),
});

const fdocApi = new FdocApi(
    new HttpTransport({
        baseUrl: configProvider.get("fdoc:url:base"),
        headers: {
            // TODO: deduplicate
            "Content-Type": "application/json",
        },
    }),
    {
        appCode: configProvider.get("fdoc:auth:appCode"),
        apiKey: configProvider.get("fdoc:auth:apiKey"),
        corpId: configProvider.get("fdoc:auth:corpId"),
    },
    configProvider.get("fdoc:url:archivesBase"),
);

const fdocAccessTokenService = new FdocAccessTokenService(
    fdocApi,
    operatorTokenService,
    medialogService,
);

const fdocService = new FdocService(
    fdocApi,
    medialogService,
    operatorTokenService,
    packageService,
    documentService,
    smbService,
    fdocAccessTokenService,
);

const cliService = new CliService(
    fdocService,
    medialogService,
);

const program = new Command()
    .option("--debug", "enables extended logging")
    .option("--debug-transport", "enables transport debug logging")
    .hook("preSubcommand", () => {
        global.console = new AppLogger(debug);
    });

program
    .name("fdoc")
    .description("FDoc CLI tool")
    .version("0.0.1");

program
    .command("server")
    .description("run server")

    .argument("[port]", "optional port, else uses config", configProvider.get("server:port"))

    .action((port: string | number) => {
        new ServerService(
            fdocService,
            medialogService,
            packageService,
        )
            .run(Number(port));
    });

{
    const getCommand = program
        .command("get")
        .description("get some value, document or data");

    getCommand
        .command("corp-id")
        .description("get static corpId value to save in config, part of initial deployment")

        .option("-l, --login <login>", undefined, configProvider.get("fdoc:auth:login"))
        .option("-p, --password <password>", undefined, configProvider.get("fdoc:auth:password"))

        .action(async (options: GetCorpIdCliParams) => {
            const corpId = await cliService.getCorpId(options);

            console.log({ corpId });
        });

    getCommand
        .command("document")
        .description("get document data")

        .requiredOption("-o, --operator-id <operator-id>", "required")

        .requiredOption("-t, --entity-type <entity-type>", "required")
        .requiredOption("-i, --entity-id <entity-id>", "required")

        .option("--mret, --medialog-reference-entity-type <medialog-reference-entity-type>")
        .option("--mrei, --medialog-reference-entity-id <medialog-reference-entity-id>")

        .action(async (options: GetDocumentCliParams) => {
            const document = await cliService.getDocument(options);

            console.dir(document);
        });

    getCommand
        .command("document-status")
        .description("get document status")

        .requiredOption("-t, --entity-type <entity-type>", "required")
        .requiredOption("-i, --entity-id <entity-id>", "required")

        .option("--mret, --medialog-reference-entity-type <medialog-reference-entity-type>")
        .option("--mrei, --medialog-reference-entity-id <medialog-reference-entity-id>")

        .action(async (options: GetDocumentStatusCliParams) => {
            const documentStatus = await cliService.getDocumentStatus(options);

            console.dir(documentStatus);
        });

    getCommand
        .command("smb-file")
        .description("get file from smb")
        .requiredOption("--address <address>", "smb server address")
        .requiredOption("--username <username>", "smb username")
        .requiredOption("--password <password>", "smb password")
        .requiredOption("--domain <domain>", "smb domain")
        .requiredOption("--path <path>", "path to file on smb server")
        .requiredOption("--destination <destination>", "path to save file locally")
        .action(async (options: SmbGetFileOptions) => {
            const smbService = new SmbService({
                address: options.address,
                username: options.username,
                password: options.password,
                domain: options.domain,
            });

            await smbService.getFile(options.path, options.destination);

            console.log(`File ${options.path} downloaded to ${options.destination}`);
        });
}

{
    const createCommand = program
        .command("create")
        .description("create entity");

    createCommand
        .command("document")
        .description("create document")

        .allowUnknownOption(true)
        .allowExcessArguments(true)

        .requiredOption("--oid, --operator-id <operator-id>", "required")

        .requiredOption("--pid, --package-id <package-id>", "required")
        .option("--pmid, --package-medialog-id <package-medialog-id>")
        .option("--pname, --package-name <package-name>")
        .option("--pni, --package-need-identification")
        .option("--poid, --package-object-id <package-object-id>")
        .option("--poas, --package-operator-auto-sign")

        .option("--did.N, --document-id.N <document-id>")
        .option("--dmid.N, --document-medialog-id.N <document-medialog-id>")
        .option("--dname.N, --document-name.N <document-name>")
        .option("--dfilep.N, --document-file-path.N <document-file-path>")
        .option("--dued.N, --document-unsign-expired-date.N <iso-date>")

        .requiredOption("--cid, --client-id <client-id>", "required")
        .requiredOption("--cname, --client-name <client-name>", "required")
        .requiredOption("--cphone, --client-phone <client-phone>", "required")
        .option("--crole, --client-role <client-role>")

        .option("--oep, --operator-employee-position <operator-employee-position>")

        .option("--pl, --payment-link <payment-link>")
        .option("--pa, --payment-amount <amount>")

        .action(async (options: CreateDocumentCliParams) => {
            const { url } = await cliService.createDocument(
                {
                    operatorId: options.operatorId,

                    packageMedialogId: options.packageMedialogId,
                    packageId: options.packageId,
                    packageName: options.packageName,
                    packageObjectId: options.packageObjectId,
                    packageNeedIdentification: options.packageNeedIdentification,
                    packageOperatorAutoSign: options.packageOperatorAutoSign,

                    clientId: options.clientId,
                    clientName: options.clientName,
                    clientPhone: options.clientPhone,
                    clientRole: options.clientRole,

                    operatorEmployeePosition: options.operatorEmployeePosition,

                    paymentLink: options.paymentLink,
                    paymentAmount: options.paymentAmount,
                },
                argv,
            );

            console.log({ url });
        });

    createCommand
        .command("document-with-signers")
        .description("create document with signers")

        .allowUnknownOption(true)
        .allowExcessArguments(true)

        .requiredOption("--oid, --operator-id <operator-id>", "required")

        .requiredOption("--pid, --package-id <package-id>", "required")
        .option("--pmid, --package-medialog-id <package-medialog-id>")
        .option("--pname, --package-name <package-name>")
        .option("--pni, --package-need-identification")
        .option("--poid, --package-object-id <package-object-id>")
        .option("--poas, --package-operator-auto-sign")
        .option("--pit, --package-identification-type <package-identification-type>")

        .option("--sid.N, --signer-id.N <signer-id>")
        .option("--sname.N, --signer-name.N <signer-name>")
        .option("--sphone.N, --signer-phone.N <signer-phone>")
        .option("--stype.N, --signer-type.N <signer-type>")
        .option("--sp.N, --signer-priority.N <signer-priority>")
        .option("--soas.N, --signer-operator-auto-sign.N <signer-operator-auto-sign>")
        .option("--scr.N, --signer-client-role.N <signer-client-role>")
        .option("--sep.N, --signer-employee-position.N <signer-employee-position>")
        .option("--sqca.N, --signer-qes-cert-alias.N <signed-qes-cert-alias>")

        .option("--did.N, --document-id.N <document-id>")
        .option("--dmid.N, --document-medialog-id.N <document-medialog-id>")
        .option("--dname.N, --document-name.N <document-name>")
        .option("--dfilep.N, --document-file-path.N <document-file-path>")
        .option("--dued.N, --document-unsign-expired-date.N <document-unsign-expired-date>")

        .option("--dtid.N, --document-template-id.N <document-template-id>")
        .option("--dtname.N, --document-template-name.N <document-template-name>")
        .option("--dtasf.N, --document-template-auto-send-to-fill.N <document-template-auto-send-to-fill>")
        .option("--dtued.N, --document-template-unsign-expired-date.N <document-template-unsign-expired-date>")

        .option("--pl, --payment-link <payment-link>")
        .option("--pa, --payment-amount <amount>")

        .action(async (options: CreateDocumentWithSignersCliParams) => {
            const { url } = await cliService.createDocumentWithSigners(
                {
                    operatorId: options.operatorId,

                    packageId: options.packageId,
                    packageMedialogId: options.packageMedialogId,
                    packageName: options.packageName,
                    packageNeedIdentification: options.packageNeedIdentification,
                    packageObjectId: options.packageObjectId,
                    packageIdentificationType: options.packageIdentificationType,
                    packageOperatorAutoSign: options.packageOperatorAutoSign,

                    paymentLink: options.paymentLink,
                    paymentAmount: options.paymentAmount,
                },
                argv,
            );

            console.log({ url });
        });
}

{
    const documentCommand = program
        .command("document")
        .description("document related commands");

    documentCommand
        .command("decline")
        .description("decline document")

        .allowUnknownOption(true)
        .allowExcessArguments(true)

        .requiredOption("--oid, --operator-id <operator-id>", "required")

        .requiredOption("--pid, --package-id <package-id>", "required")
        .option("--dt, --decline-type <decline-type>")

        .action(async (options: DeclineDocumentCliParams) => {
            console.debug("cli.declineDocument.args", { options, argv });

            await cliService.declineDocument(
                {
                    operatorId: options.operatorId,

                    packageId: options.packageId,
                    declineType: options.declineType,
                },
            );

            console.info(true);
        });

    documentCommand
        .command("sync-new-signed")
        .description("synchronize new signed documents")

        .requiredOption("--oid, --operator-id <operator-id>", "required")

        .action(async (options: SyncNewSignedDocumentsCliParams) => {
            await cliService.syncNewSignedDocuments(options);

            console.info(true);
        });
}

try {
    await program.parseAsync(argv, { from: "user" });
}
catch (e) {
    if (e instanceof ZodError) {
        console.dir(transformIssues(e.issues), { depth: null });
    }
    else console.error(e);
}
