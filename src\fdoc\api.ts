import type { Readable } from "node:stream";
import type { HttpTransport } from "../transport";
import type { DocumentWithUrlType, DocumentWithStatusType } from "./schema";

import querystring from "node:querystring";

import { sleep } from "../utils";
import { z, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "..";
import { Schema, defaultError, fallbackError } from "./schema";

export class FdocApi {
    protected readonly appCode: string;
    protected readonly apiKey: string;
    protected readonly corpId: string;

    constructor(
        protected readonly transport: HttpTransport,
        config: {
            appCode: string;
            apiKey: string;
            corpId: string;
        },
        protected readonly archivesBaseUrl: string,
    ) {
        this.appCode = config.appCode;
        this.apiKey = config.apiKey;
        this.corpId = config.corpId;
    }

    /** @returns only encoded value itself, not header's one. */
    protected encodeBasicAuth(params: {
        login: string;
        password: string;
    }) {
        return Buffer
            .from(`${params.login}:${params.password}`, "utf8")
            .toString("base64");
    }

    protected parseResponse<
        TSuccess extends z.ZodSchema,
    >(
        response: {
            success: boolean;
            body: string;
        },
        successSchema?: TSuccess,
        errorSchema: z.ZodSchema = defaultError,
    ): TSuccess["_output"] {
        let data: NonNullable<unknown> | undefined;

        try {
            data = JSON.parse(response.body);
        }
        catch (e) {
            if (!successSchema) {
                console.error("fdocApi.parseResponse.json.error", e);
                console.error("fdocApi.parseResponse.json.response", response);

                throw e;
            }
        }

        if (response.success) {
            if (successSchema) {
                const success = successSchema.safeParse(data);

                if (success.success) {
                    return success.data;
                }

                throw success.error;
            }

            return data;
        }

        const expectedError = errorSchema.safeParse(data);

        if (expectedError.success) {
            if ("message" in expectedError.data) {
                throw new Error(expectedError.data.message);
            }

            throw new Error(JSON.stringify(expectedError.data));
        }

        const unexpectedError = fallbackError.safeParse(data);

        if (unexpectedError.success) {
            throw new Error(JSON.stringify(unexpectedError.data));
        }

        throw new Error(`Unable to parse response: '${JSON.stringify(response)}'`);
    }

    protected parseResponseOrFailGracefully<
        TSuccess extends z.ZodSchema,
    >(
        response: {
            success: boolean;
            status: number;
            body: string;
        },
        successSchema?: TSuccess,
        errorSchema: z.ZodSchema = defaultError,
    ) {
        try {
            return this.parseResponse(
                response,
                successSchema,
                errorSchema,
            );
        }
        catch (e) {
            throw new Error(`${response.status} - ${e instanceof Error ? e.message : String(e)}`);
        }
    }

    async verifyApiKey(params: {
        login: string;
        password: string;
    }) {
        console.debug("fdocApi.verifyApiKey.params", params);

        const auth = this.encodeBasicAuth(params);

        const { query, headers } = ZodHelper.parseInput(Schema.verifyApiKey.request, {
            query: {
                apiKey: this.apiKey,
            },

            headers: {
                Authorization: `Basic ${auth}`,
            },
        });

        const { response, status, success } = await this.transport.send(
            `/verifyApiKey?${querystring.stringify(query)}`,
            null,
            {
                method: "get",
                headers,
            },
        );

        return this.parseResponseOrFailGracefully(
            {
                body: response,
                success,
                status,
            },
            Schema.verifyApiKey.response.success,
        );
    }

    async createWebhook(params: {
        login: string;
        password: string;
        events: {
            url: string;
            code: "SetStatus" | "SetStatusPackageByClient" | "SetStatusMessage" | "CreateDocument";
        }[];
        channelTypeCode?: "API" | "LK" | "BITRIX24";
    }) {
        console.debug("fdocApi.createWebhook.params", params);

        const auth = this.encodeBasicAuth({
            login: params.login,
            password: params.password,
        });

        const { body, headers } = ZodHelper.parseInput(Schema.corp.webhooks.create.request, {
            headers: {
                Authorization: `Basic ${auth}`,
            },

            body: {
                apiKey: this.apiKey,
                events: params.events,
                channelTypeCode: params.channelTypeCode,
            },
        });

        const { response, status, success } = await this.transport.send(
            "/corp/webhooks",
            body,
            {
                method: "put",
                headers,
            },
        );

        return this.parseResponseOrFailGracefully(
            {
                body: response || "{}",
                success,
                status,
            },
        );
    }

    async operatorAccessToken(
        params:
            | {
                type: "basic";
                login: string;
                password: string;
            }
            | {
                type: "token";
                refreshToken: string;
            },
    ) {
        let grantType: "password" | "refreshToken";
        let grant: string;

        switch (params.type) {
            case "basic": {
                grantType = "password";
                grant = this.encodeBasicAuth(params);

                break;
            }

            case "token": {
                grantType = "refreshToken";
                grant = params.refreshToken;

                break;
            }
        }

        const { headers, body } = ZodHelper.parseInput(Schema.operator.accessToken.request, {
            body: {
                app: this.appCode,
                apiKey: this.apiKey,
                corpId: this.corpId,

                grantType,
                grant,
            },
        });

        const { response, status, success } = await this.transport.send(
            "/operator/accessToken",
            body,
            {
                method: "post",
                headers,
            },
        );

        return this.parseResponseOrFailGracefully(
            {
                body: response,
                success,
                status,
            },
            Schema.operator.accessToken.response.success,
        );
    }

    async getDocument(params: {
        accessToken: string; // Bearer
        entityType: DocumentWithUrlType;
        entityId: string;
    }) {
        const { query, headers } = ZodHelper.parseInput(Schema.document.get.request, {
            query: {
                corpId: this.corpId,
                app: this.appCode,

                id: params.entityId,
                idType: params.entityType,
            },

            headers: {
                "Authorization": `Bearer ${params.accessToken}`,
            },
        });

        const { response, status, success } = await this.transport.send(
            `/document?${querystring.stringify(query)}`,
            null,
            {
                method: "get",
                headers,
            },
        );

        return this.parseResponseOrFailGracefully(
            {
                body: response,
                success,
                status,
            },
            Schema.document.get.response.success,
        );
    }

    async getDocumentStatus(params: {
        entityType: DocumentWithStatusType;
        entityId: string;
    }) {
        const { query, headers } = ZodHelper.parseInput(Schema.document.getStatus.request, {
            query: {
                id: params.entityId,
                idType: params.entityType,
            },
        });

        const { response, status, success } = await this.transport.send(
            `/document/status?${querystring.stringify(query)}`,
            null,
            {
                method: "get",
                headers,
            },
        );

        return this.parseResponseOrFailGracefully(
            {
                body: response,
                success,
                status,
            },
            Schema.document.getStatus.response.success,
        );
    }

    // TODO: Настоятельно рекомендуем заполнять "document_id", "package_id", "documentTemplates" в формате guid. (doc v1.25 page 21)
    async createDocument(params: {
        accessToken: string;

        package: {
            id: string; // may = document.id
            name?: string;
            objectId?: string;

            /** TODO: optional, requires FDoc assistance (doc v1.25 page 14 of 98) */
            needIdentification?: boolean;

            operatorAutoSign?: boolean;
        };

        documents: {
            id: string;
            name: string; // file name with ext
            file: Buffer;
            unsignExpiredDate?: Date | null;
        }[];

        client: {
            id: string;
            phone: string;
            name: string;

            /** TODO: optional, requires FDoc assistance (doc v1.25 page 14 of 98) */
            clientRole?: string;
        };

        operator?: {
            /** TODO: optional, requires FDoc assistance (doc v1.25 page 14 of 98) */
            employeePosition?: string;
        };

        payment?: {
            packagePaymentLink?: string;
            amount?: number; // double
        };
    }) {
        const {
            documents,
            client,
            operator,
            payment,
        } = params;

        const { headers, body } = ZodHelper.parseInput(Schema.document.create.request, {
            headers: {
                "Authorization": `Bearer ${params.accessToken}`,
            },

            body: {
                package: params.package,
                client,
                operator,
                payment,

                documents: documents.map(document => ({
                    ...document,
                    file: document.file.toString("base64"),
                    unsignExpiredDate: document.unsignExpiredDate ?? null,
                })),
            },
        });

        const { response, status, success } = await this.transport.send(
            "/document",
            body,
            {
                method: "post",
                headers,
            },
        );

        return this.parseResponseOrFailGracefully(
            {
                body: response,
                success,
                status,
            },
            Schema.document.create.response.success,
        );
    }

    // TODO: Настоятельно рекомендуем заполнять "document_id", "package_id", "documentTemplates" в формате guid. (doc v1.25 page 21)
    async createDocumentWithSigners(params: {
        accessToken: string;

        // isAdditionalServicesCanBeAdded?: boolean;

        package: {
            id: string; // may = document.id
            name?: string;
            objectId?: string;

            /** TODO: optional, requires FDoc assistance (doc v1.25 page 14 of 98) */
            needIdentification?: boolean;

            identificationType?: string;

            signers: {
                id?: string;
                name?: string;
                phone?: string;

                type: "corp" | "client";
                priority: number; // int

                operatorAutoSign?: boolean;

                /** TODO: optional, requires FDoc assistance (doc v1.25 page 14 of 98) */
                clientRole?: string;

                /** TODO: optional, requires FDoc assistance (doc v1.25 page 14 of 98) */
                employeePosition?: string;

                qesCertAlias?: string;
            }[];
        };

        documents: {
            id: string;
            name: string; // file name with ext
            file: Buffer;
            unsignExpiredDate?: Date | null;
        }[];

        /** TODO: optional, requires FDoc assistance (doc v1.25 page 14 of 98) */
        documentTemplates: {
            id: string;
            name?: string;
            autoSendToFill?: boolean;
            unsignExpiredDate?: Date | null;
        }[];

        payment?: {
            packagePaymentLink?: string;
            amount?: number; // double
        };
    }) {

        const { headers, body } = ZodHelper.parseInput(Schema.document.createWithSigners.request, {
            headers: {
                "Authorization": `Bearer ${params.accessToken}`,
            },

            body: {
                addService: false,

                package: params.package,
                payment: params.payment,

                documents: params.documents.map(document => ({
                    ...document,
                    file: document.file.toString("base64"),
                    unsignExpiredDate: document.unsignExpiredDate ?? null,
                })),

                documentTemplates: params.documentTemplates.map(documentTemplate => ({
                    ...documentTemplate,
                    unsignExpiredDate: documentTemplate.unsignExpiredDate ?? null,
                })),
            },
        });

        const { response, status, success } = await this.transport.send(
            "/document/withSigners",
            body,
            {
                method: "post",
                headers,
            },
        );

        return this.parseResponseOrFailGracefully(
            {
                body: response,
                success,
                status,
            },
            Schema.document.createWithSigners.response.success,
        );
    }

    async declineDocument(params: {
        accessToken: string;

        packageGuid: string;
        declineType: string | null;
    }) {
        const { headers, body } = ZodHelper.parseInput(Schema.document.decline.request, {
            headers: {
                "Authorization": `Bearer ${params.accessToken}`,
            },

            body: {
                packageGuid: params.packageGuid,
                declineType: params.declineType,
            },
        });

        const { response, status, success } = await this.transport.send(
            "/employee/decline/package",
            body,
            {
                method: "post",
                headers,
            },
        );

        return this.parseResponseOrFailGracefully(
            {
                body: response,
                success,
                status,
            },
            Schema.document.decline.response.success,
        );
    }

    async getArchivesDocumentToken(params: {
        accessToken: string;

        entityType: DocumentWithStatusType;
        entityId: string;

        isNeedQesSign?: boolean;
    }) {
        const { headers, body } = ZodHelper.parseInput(
            Schema.corp.archives.document.getToken.request,
            {
                headers: {
                    "Authorization": `Bearer ${params.accessToken}`,
                    "Content-Type": "application/json",
                },

                body: {
                    needQesSign: Boolean(params.isNeedQesSign),

                    idType: params.entityType,
                    id: params.entityId,
                },
            },
        );

        const { response, status, success } = await this.transport.send(
            "/corp/archives/document",
            body,
            {
                method: "post",
                headers,
            },
        );

        return this.parseResponseOrFailGracefully(
            {
                body: response,
                success,
                status,
            },
            Schema.corp.archives.document.getToken.response.success,
        );
    }

    async getArchivesDownloadData(params: {
        token: string;
    }) {
        const { headers, body } = ZodHelper.parseInput(
            Schema.archiveServer.getDownloadData.request,
            {
                headers: {
                    "Content-Type": "application/json",
                },

                body: {
                    token: params.token,
                },
            },
        );

        const { response, status, success } = await this.transport.send(
            "/archive/package",
            body,
            {
                method: "post",
                headers,
                baseUrl: this.archivesBaseUrl,
            },
        );

        return this.parseResponseOrFailGracefully(
            {
                body: response,
                success,
                status,
            },
            Schema.archiveServer.getDownloadData.response.success,
        );
    }

    async getArchivesDownloadDataWithWaiting(params: {
        token: string;
    }) {
        // eslint-disable-next-line no-constant-condition
        while (true) {
            const result = await this.getArchivesDownloadData({
                token: params.token,
            });

            if (result.data.status === "SUCCESS") {
                return result;
            }

            if (result.data.status === "ERROR") {
                throw new Error(`Unexpected error on downloading archives: ${JSON.stringify(result.data)}`);
            }

            if (result.data.status === "PARTLY") {
                throw new Error(`Partial error on downloading archives: ${JSON.stringify(result.data)}`);
            }

            await sleep(1000);
        }
    }

    async downloadArchivesDocument(params: {
        token: string;
        guid: string;
    }) {
        const { params: { token, guid } } = ZodHelper.parseInput(
            Schema.archiveServer.download.request,
            {
                params: {
                    token: params.token,
                    guid: params.guid,
                },
            },
        );

        const { response, status, success } = await this.transport.send<Readable>(
            `/archive/${token}/download/${guid}`,
            null,
            {
                method: "get",
                baseUrl: this.archivesBaseUrl,

                axios: {
                    responseType: "stream",
                },
            },
        );

        if (!success) {
            throw new Error(`${status} - ${response}`);
        }

        return response;
    }
}
