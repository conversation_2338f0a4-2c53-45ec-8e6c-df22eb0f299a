/* eslint-disable no-var */

declare var debug: boolean;
declare var debugTransport: boolean;

declare type Primitive = string | number | boolean | null | undefined;

declare type Maybe<T> = T | undefined;

declare type MaybePromise<T> = Promise<T> | T;

declare type SoftUnion<T1, T2> =
    | (
        {
            [Key in keyof T1]: T1[Key];
        } & Partial<Record<keyof T2, never>>
    )
    | (
        {
            [Key in keyof T2]: T2[Key]
        } & Partial<Record<keyof T1, never>>
    );

declare type Normalize<T> = { [Key in keyof T]: T[Key] } & {};

declare type DefinedValue = NonNullable<unknown> | null;

declare type DeepPartial<T> = T extends object ? {
    [P in keyof T]?: DeepPartial<T[P]>;
} : T;

declare type DeepOr<T, TOr = string> =
    NonNullable<T> extends object
        ? T extends Date | RegExp
            ? T | TOr
            : T extends readonly unknown[]
                ? DeepOr<T[number], TOr>[]
                : { [Key in keyof T]: DeepOr<T[Key], TOr> }
        : T | TOr;

declare type DeepPartialOr<T, TOr = string> =
    NonNullable<T> extends object
        ? T extends Date | RegExp
            ? T | TOr
            : T extends readonly unknown[]
                ? DeepPartialOr<T[number], TOr>[]
                : Partial<{ [Key in keyof T]: DeepPartialOr<T[Key], TOr> }>
        : T | TOr;
