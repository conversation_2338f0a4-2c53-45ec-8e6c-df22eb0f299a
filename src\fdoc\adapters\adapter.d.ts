import type { Entity as Document } from "../../document";

export type SignedDocuments = {
    files: {
        localPath: string;
        medialogFileName: string;
        attachToEmk: boolean;
    }[];
};

export type DownloadFilesParams = {
    operatorId?: number;
    document: Document;
    type: "bes" | "qes";
};

export interface SignedDocumentAdapter {
    downloadFiles(params: DownloadFilesParams): Promise<SignedDocuments>;
}
