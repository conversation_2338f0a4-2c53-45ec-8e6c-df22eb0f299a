import type { BaseEntity, BaseModel, BaseRepository } from "../common";

export type OperatorToken = BaseEntity & {
    operatorId: number;
    accessToken: string;
    accessTokenExpiresAt: Date;
    refreshToken: string;
    refreshTokenExpiresAt: Date;
};

export type CreateDto = Pick<
    OperatorToken,
    | "operatorId"
    | "accessToken"
    | "accessTokenExpiresAt"
    | "refreshToken"
    | "refreshTokenExpiresAt"
>;

export type UpdateDto = Partial<
    Pick<
        OperatorToken,
        | "accessToken"
        | "accessTokenExpiresAt"
        | "refreshToken"
        | "refreshTokenExpiresAt"
    >
>;

export type OperatorTokenRepository = BaseRepository<BaseModel & OperatorToken>;
