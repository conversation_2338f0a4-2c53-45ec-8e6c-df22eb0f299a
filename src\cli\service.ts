import type { FdocService } from "../fdoc/service";
import type { MedialogService } from "../medialog/service";
import type { UnsafeCommandInput } from "./abstract-command";

import minimist from "minimist";

import { groupIndexedArgs } from "../utils";
import {
    GetCorpIdCommand,
    GetDocumentCommand,
    GetDocumentStatusCommand,
    CreateDocumentCommand,
    CreateDocumentWithSignersCommand,
    DeclineDocumentCommand,
    SyncNewSignedDocumentsCommand,
} from "./command";
import path from "node:path";
import type { Schema } from "./schema";

type UrlObject = { url: string };

type DocumentStatus = typeof Schema.document.getStatus.result._type;

export type GetCorpIdCliParams = Partial<{
    login: string;
    password: string;
}>;

export type GetDocumentCliParams = Partial<{
    operatorId: string;
    entityType: string;
    entityId: string;
    medialogReferenceEntityType: string;
    medialogReferenceEntityId: number;
}>;

export type GetDocumentStatusCliParams = Partial<{
    entityType: string;
    entityId: string;
    medialogReferenceEntityType: string;
    medialogReferenceEntityId: number;
}>;

export type CreateDocumentCliParams = Partial<{
    operatorId: string;

    packageMedialogId: number;
    packageId: string;
    packageName: string;
    packageNeedIdentification: boolean;
    packageObjectId: string;
    packageOperatorAutoSign: boolean;

    clientId: string;
    clientName: string;
    clientPhone: string;
    clientRole: string;

    operatorEmployeePosition: string;

    paymentLink: string;
    paymentAmount: string;
}>;

export type CreateDocumentWithSignersCliParams = Partial<{
    operatorId: string;

    addService: boolean;

    packageMedialogId: number;
    packageId: string;
    packageName: string;
    packageNeedIdentification: boolean;
    packageObjectId: string;
    packageOperatorAutoSign: boolean;
    packageIdentificationType: string;

    paymentLink: string;
    paymentAmount: string;
}>;

export type DeclineDocumentCliParams = Partial<{
    operatorId: string;
    packageId: string;
    declineType: string;
}>;

export type SyncNewSignedDocumentsCliParams = Partial<{
    operatorId: string;
}>;

export interface ICliService {
    getCorpId(params: GetCorpIdCliParams): Promise<string>;
    getDocument(params: GetDocumentCliParams): Promise<UrlObject>;
    getDocumentStatus(params: GetDocumentStatusCliParams): Promise<DocumentStatus>;

    createDocument(
        params: CreateDocumentCliParams,
        argv: string[],
    ): Promise<UrlObject>;
    createDocumentWithSigners(
        params: CreateDocumentWithSignersCliParams,
        argv: string[],
    ): Promise<UrlObject>;
    syncNewSignedDocuments(params: SyncNewSignedDocumentsCliParams): Promise<void>;
}

export class CliService implements ICliService {
    constructor(
        protected readonly fdocService: FdocService,
        protected readonly medialogService: MedialogService,
    ) { }

    protected parseArgs<T extends Record<string, unknown>>(argv: string[]): Partial<T> {
        return minimist(argv) as Record<string, unknown> as Partial<T>;
    }

    async getCorpId(params: GetCorpIdCliParams) {
        return await new GetCorpIdCommand(this.fdocService)
            .executeWithUnknownArgs(params)
            .then((result) => result.corpId);
    }

    protected parseMedialogReference(params: GetDocumentCliParams) {
        return {
            entityType: params.medialogReferenceEntityType,
            entityId: params.medialogReferenceEntityId,
        };
    }

    async getDocument(params: GetDocumentCliParams) {
        const medialogReference = this.parseMedialogReference(params);

        return await new GetDocumentCommand(this.fdocService)
            .executeWithUnknownArgs({
                operatorId: params.operatorId,

                medialogReference,

                entityType: params.entityType,
                entityId: params.entityId,
            });
    }

    async getDocumentStatus(params: GetDocumentStatusCliParams) {
        const medialogReference = this.parseMedialogReference(params);

        return await new GetDocumentStatusCommand(this.fdocService)
            .executeWithUnknownArgs({
                medialogReference,

                entityType: params.entityType,
                entityId: params.entityId,
            });
    }

    protected parseSigners(argv: string[]) {
        const entries = groupIndexedArgs(
            argv,
            [
                {
                    aliases: ["sid", "signer-id"],
                    as: "id",
                },
                {
                    aliases: ["sname", "signer-name"],
                    as: "name",
                },
                {
                    aliases: ["sphone", "signer-phone"],
                    as: "phone",
                },
                {
                    aliases: ["stype", "signer-type"],
                    as: "type",
                },
                {
                    aliases: ["sp", "signer-priority"],
                    as: "priority",
                },
                {
                    aliases: ["soas", "signer-operator-auto-sign"],
                    as: "operatorAutoSign",
                },
                {
                    aliases: ["scr", "signer-client-role"],
                    as: "clientRole",
                },
                {
                    aliases: ["sep", "signer-employee-position"],
                    as: "employeePosition",
                },
                {
                    aliases: ["sqca", "signer-qes-cert-alias"],
                    as: "qesCertAlias",
                },
            ],
        );

        function isValidType(type: string): type is "corp" | "client" {
            return type === "corp" || type === "client";
        }

        const result = entries.map((entry, i) => {
            const {
                id,
                name,
                phone,

                type,
                priority: priorityString,

                operatorAutoSign,
                clientRole,
                employeePosition,
                qesCertAlias,
            } = entry;

            if (type && priorityString) {
                if (!isValidType(type)) {
                    throw new Error(`Broken signer entry #${i}, invalid type '${type}', must be 'corp' or 'client'.`);
                }

                if (type === "corp" && !(id || (name && phone))) {
                    throw new Error(`Broken signer entry #${i}, invalid params for type 'corp': must be at least id or (name + phone).`);
                }

                if (type === "client" && !(id && name && phone)) {
                    throw new Error(`Broken signer entry #${i}, invalid params for type 'client': must be at least id, name, phone.`);
                }

                const priority = Number(priorityString);

                if (Number.isNaN(priority)) {
                    throw new Error(`Broken signer entry #${i}, invalid priority '${priorityString}', must be a number.`);
                }

                return {
                    id,
                    name,
                    phone,

                    type,
                    priority,

                    operatorAutoSign: Boolean(operatorAutoSign),
                    clientRole,
                    employeePosition,
                    qesCertAlias,
                };
            }

            throw new Error(`Broken signer entry #${i}: ${JSON.stringify(entry, null, 2)}`);
        });

        return result;
    }

    protected parseDocuments(argv: string[]) {
        const entries = groupIndexedArgs(
            argv,
            [
                {
                    aliases: ["dmid", "document-medialog-id"],
                    as: "medialogId",
                },
                {
                    aliases: ["did", "document-id"],
                    as: "id",
                },
                {
                    aliases: ["dname", "document-name"],
                    as: "name",
                },
                {
                    aliases: ["dfilep", "document-file-path"],
                    as: "filePath",
                },
                {
                    aliases: ["dued", "document-unsign-expired-date"],
                    as: "unsignExpiredDate",
                },
            ],
        );

        const result = entries.map((entry, i) => {
            const {
                id,
                filePath,
                unsignExpiredDate,
            } = entry;

            const name = entry.name ?? (filePath && path.basename(filePath));

            if (id && name && filePath) {
                return {
                    medialogId: Number(entry.medialogId),
                    id,
                    name,
                    filePath,
                    unsignExpiredDate,
                };
            }

            throw new Error(`Broken document entry #${i}: ${JSON.stringify(entry, null, 2)}`);
        });

        return result;
    }

    protected parseDocumentTemplates(argv: string[]) {
        const entries = groupIndexedArgs(
            argv,
            [
                {
                    aliases: ["dtid", "document-template-id"],
                    as: "id",
                },
                {
                    aliases: ["dtname", "document-template-name"],
                    as: "name",
                },
                {
                    aliases: ["dtasf", "document-template-auto-send-to-fill"],
                    as: "autoSendToFill",
                },
                {
                    aliases: ["dtued", "document-template-unsign-expired-date"],
                    as: "unsignExpiredDate",
                },
            ],
        );

        const result = entries.map((entry, i) => {
            const {
                id,
                name,
                autoSendToFill,
                unsignExpiredDate,
            } = entry;

            if (id) {
                return {
                    id,
                    name,
                    autoSendToFill,
                    unsignExpiredDate,
                };
            }

            throw new Error(`Broken document template entry #${i}: ${JSON.stringify(entry, null, 2)}`);
        });

        return result;
    }

    async createDocument(
        params: CreateDocumentCliParams,
        argv: string[],
    ) {
        const documents = this.parseDocuments(argv);

        const commandParams: UnsafeCommandInput<CreateDocumentCommand> = {
            operatorId: params.operatorId,

            package: {
                medialogId: params.packageMedialogId,
                id: params.packageId,
                name: params.packageName,
                needIdentification: params.packageNeedIdentification,
                objectId: params.packageObjectId,
                operatorAutoSign: params.packageOperatorAutoSign,
            },

            documents,

            client: {
                id: params.clientId,
                name: params.clientName,
                phone: params.clientPhone,
                clientRole: params.clientRole,
            },

            operator: {
                employeePosition: params.operatorEmployeePosition,
            },

            payment: {
                packagePaymentLink: params.paymentLink,
                amount: params.paymentAmount,
            },
        };

        return await new CreateDocumentCommand(this.fdocService)
            .executeWithUnknownArgs(commandParams);
    }

    async createDocumentWithSigners(
        params: CreateDocumentWithSignersCliParams,
        argv: string[],
    ) {
        const signers = this.parseSigners(argv);
        const documents = this.parseDocuments(argv);
        const documentTemplates = this.parseDocumentTemplates(argv);

        const commandParams: UnsafeCommandInput<CreateDocumentWithSignersCommand> = {
            operatorId: params.operatorId,

            addService: params.addService,

            package: {
                medialogId: params.packageMedialogId,
                id: params.packageId,
                name: params.packageName,
                needIdentification: params.packageNeedIdentification,
                objectId: params.packageObjectId,
                identificationType: params.packageIdentificationType,

                signers,
            },

            documents,
            documentTemplates,

            payment: {
                packagePaymentLink: params.paymentLink,
                amount: params.paymentAmount,
            },
        };

        return await new CreateDocumentWithSignersCommand(this.fdocService)
            .executeWithUnknownArgs(commandParams);
    }

    async declineDocument(params: DeclineDocumentCliParams) {
        return await new DeclineDocumentCommand(this.fdocService)
            .executeWithUnknownArgs(params);
    }

    async syncNewSignedDocuments(params: SyncNewSignedDocumentsCliParams) {
        return await new SyncNewSignedDocumentsCommand(this.fdocService)
            .executeWithUnknownArgs(params);
    }
}
