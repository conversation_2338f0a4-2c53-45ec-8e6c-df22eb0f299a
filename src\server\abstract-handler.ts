import type { Request, Response } from "express";
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "..";

type BasicResponse = {
    status: number;
    body: Record<string, unknown> | string | null;
}

export type HandlerRequest<T extends AbstractHandler<BasicResponse>> =
    T extends AbstractHandler<BasicResponse, infer TRequest, unknown>
        ? DeepPartialOr<TRequest>
        : never;

export abstract class AbstractHandler<
    TResponse extends BasicResponse = BasicResponse,
    TRequestInput = unknown,
    TRequestOutput = TRequestInput,
> {
    constructor(
        protected readonly transformRequest?: ZodHelper.Transform<TRequestOutput> | null,
        protected readonly assertResponse?: ZodHelper.Assert<TResponse> | null,
    ) {}

    get handler() {
        return this.handleRequest.bind(this);
    }

    /**
     * @sealed preferred
     */
    async handleRequest(req: Request, res: Response): Promise<void> {
        try {
            const requestData = {
                params: req.params,
                query: req.query,
                headers: req.headers,
                body: req.body,
            };

            console.debug(
                `${this.constructor.name}.executeWithUnknownArgs.arguments`,
                {
                    transformRequest: this.transformRequest,
                    assertResponse: this.assertResponse,
                    requestData,
                },
            );

            const validRequestData = this.transformRequest ? this.transformRequest(requestData) : (requestData as TRequestOutput);
            console.debug(`${this.constructor.name}.validRequestData`, validRequestData);

            const response = await this.execute(validRequestData);
            console.debug(`${this.constructor.name}.result`, response);

            this.assertResponse?.(response);

            console.debug(`${this.constructor.name}.response.valid`, response);

            if (response.body === null) {
                res.sendStatus(response.status);

                return;
            }

            res.status(response.status);

            if (typeof response.body === "string") {
                res.send(response.body);

                return;
            }

            res.json(response.body);
        }
        catch (e) {
            console.error(`Error in ${this.constructor.name}:`);
            console.error(e);

            if (debug) {
                res
                    .status(500)
                    .json({ message: e instanceof Error ? e.message : String(e) });
            }
            else {
                res.sendStatus(500);
            }
        }
    }

    /**
     * Method to override.
     */
    abstract execute(params: TRequestOutput): Promise<TResponse>;
}
