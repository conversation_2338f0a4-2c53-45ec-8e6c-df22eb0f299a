import type { z } from "zod";

export type CommandArgs<T extends AbstractCommand> =
    T extends AbstractCommand<infer TArgs>
        ? TArgs["_input"]
        : never;

export type UnsafeCommandInput<T extends AbstractCommand> =
    T extends AbstractCommand<infer TArgs>
        ? DeepPartial<TArgs["_input"]>
        : never;

export abstract class AbstractCommand<
    TArgs extends z.ZodType = z.ZodType,
    TResult extends z.ZodType = z.ZodType,
> {
    constructor(
        protected readonly argsSchema: TArgs,
        protected readonly resultSchema: TResult,
    ) { }

    /**
     * @sealed preferred
     */
    async executeWithUnknownArgs(args: unknown): Promise<TResult["_output"]> {
        console.debug(
            `${this.constructor.name}.args`,
            args,
        );

        const parsedArgs = this.argsSchema.parse(args);
        console.debug(`${this.constructor.name}.parsedArgs`, parsedArgs);

        const result = await this.execute(parsedArgs);

        const parsedResult = this.resultSchema.parse(result);
        console.debug(`${this.constructor.name}.parsedResult`, parsedResult);

        return parsedResult;
    }

    /**
     * Method to override.
     */
    abstract execute(args: TArgs["_output"]): Promise<TResult["_output"]>;
}
